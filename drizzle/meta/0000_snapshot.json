{"id": "b573d294-e5fc-4c2c-a27c-8256b1fd71f2", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.ActivityLog": {"name": "ActivityLog", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "userId": {"name": "userId", "type": "uuid", "primaryKey": false, "notNull": false}, "ip": {"name": "ip", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "userAgent": {"name": "userAgent", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "path": {"name": "path", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "method": {"name": "method", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "queryParams": {"name": "queryParams", "type": "text", "primaryKey": false, "notNull": false}, "referer": {"name": "referer", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "database": {"name": "database", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "eventType": {"name": "eventType", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "sessionId": {"name": "sessionId", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"activity_log_created_at_idx": {"name": "activity_log_created_at_idx", "columns": [{"expression": "createdAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "activity_log_event_type_idx": {"name": "activity_log_event_type_idx", "columns": [{"expression": "eventType", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "activity_log_database_idx": {"name": "activity_log_database_idx", "columns": [{"expression": "database", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "activity_log_session_id_idx": {"name": "activity_log_session_id_idx", "columns": [{"expression": "sessionId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "activity_log_ip_idx": {"name": "activity_log_ip_idx", "columns": [{"expression": "ip", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "activity_log_user_id_idx": {"name": "activity_log_user_id_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "activity_log_created_at_event_type_idx": {"name": "activity_log_created_at_event_type_idx", "columns": [{"expression": "createdAt", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "eventType", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "activity_log_created_at_database_idx": {"name": "activity_log_created_at_database_idx", "columns": [{"expression": "createdAt", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "database", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "activity_log_session_id_event_type_idx": {"name": "activity_log_session_id_event_type_idx", "columns": [{"expression": "sessionId", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "eventType", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"ActivityLog_userId_User_id_fk": {"name": "ActivityLog_userId_User_id_fk", "tableFrom": "ActivityLog", "tableTo": "User", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.BlockedIp": {"name": "BlockedIp", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "ip": {"name": "ip", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "reason": {"name": "reason", "type": "text", "primaryKey": false, "notNull": false}, "expiresAt": {"name": "expiresAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"BlockedIp_ip_unique": {"name": "BlockedIp_ip_unique", "nullsNotDistinct": false, "columns": ["ip"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Company": {"name": "Company", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "companyName": {"name": "companyName", "type": "text", "primaryKey": false, "notNull": true}, "companyCode": {"name": "companyCode", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "companyShortName": {"name": "companyShortName", "type": "text", "primaryKey": false, "notNull": false}, "region": {"name": "region", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "province": {"name": "province", "type": "text", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "website": {"name": "website", "type": "text", "primaryKey": false, "notNull": false}, "establishDate": {"name": "establishDate", "type": "timestamp", "primaryKey": false, "notNull": false}, "registeredCapital": {"name": "registeredCapital", "type": "text", "primaryKey": false, "notNull": false}, "legalRepresentative": {"name": "legalRepresentative", "type": "text", "primaryKey": false, "notNull": false}, "businessScope": {"name": "businessScope", "type": "text", "primaryKey": false, "notNull": false}, "companyType": {"name": "companyType", "type": "text", "primaryKey": false, "notNull": false}, "industryCategory": {"name": "industryCategory", "type": "text", "primaryKey": false, "notNull": false}, "isListed": {"name": "isListed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "stockCode": {"name": "stockCode", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "database": {"name": "database", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "businessKey": {"name": "businessKey", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "businessKeyHash": {"name": "businessKeyHash", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "dataVersion": {"name": "dataVersion", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "importedAt": {"name": "importedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"company_database_idx": {"name": "company_database_idx", "columns": [{"expression": "database", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "company_business_key_idx": {"name": "company_business_key_idx", "columns": [{"expression": "businessKey", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "company_business_key_hash_idx": {"name": "company_business_key_hash_idx", "columns": [{"expression": "businessKeyHash", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"Company_businessKey_unique": {"name": "Company_businessKey_unique", "nullsNotDistinct": false, "columns": ["businessKey"]}, "Company_businessKeyHash_unique": {"name": "Company_businessKeyHash_unique", "nullsNotDistinct": false, "columns": ["businessKeyHash"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ContactSubmission": {"name": "ContactSubmission", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "subject": {"name": "subject", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "ip": {"name": "ip", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "userAgent": {"name": "userAgent", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'pending'"}, "adminNotes": {"name": "adminNotes", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"contact_submission_email_idx": {"name": "contact_submission_email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "contact_submission_category_idx": {"name": "contact_submission_category_idx", "columns": [{"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "contact_submission_status_idx": {"name": "contact_submission_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "contact_submission_created_at_idx": {"name": "contact_submission_created_at_idx", "columns": [{"expression": "createdAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.DataChangeLog": {"name": "DataChangeLog", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "businessKey": {"name": "businessKey", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "businessKeyHash": {"name": "businessKeyHash", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "operation": {"name": "operation", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "changeReason": {"name": "changeReason", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "importedBy": {"name": "importedBy", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "importedFrom": {"name": "importedFrom", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "oldData": {"name": "oldData", "type": "json", "primaryKey": false, "notNull": false}, "newData": {"name": "newData", "type": "json", "primaryKey": false, "notNull": false}}, "indexes": {"data_change_log_business_key_idx": {"name": "data_change_log_business_key_idx", "columns": [{"expression": "businessKey", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "data_change_log_business_key_hash_idx": {"name": "data_change_log_business_key_hash_idx", "columns": [{"expression": "businessKeyHash", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "data_change_log_operation_idx": {"name": "data_change_log_operation_idx", "columns": [{"expression": "operation", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "data_change_log_created_at_idx": {"name": "data_change_log_created_at_idx", "columns": [{"expression": "createdAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.DatabaseConfig": {"name": "DatabaseConfig", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "accessLevel": {"name": "accessLevel", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'free'"}, "tableName": {"name": "tableName", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "modelName": {"name": "modelName", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "maxExportLimit": {"name": "maxExportLimit", "type": "integer", "primaryKey": false, "notNull": true, "default": 10000}, "defaultExportLimit": {"name": "defaultExportLimit", "type": "integer", "primaryKey": false, "notNull": true, "default": 1000}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "sortOrder": {"name": "sortOrder", "type": "integer", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "defaultSort": {"name": "defaultSort", "type": "json", "primaryKey": false, "notNull": false}, "exportConfig": {"name": "exportConfig", "type": "json", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"DatabaseConfig_code_unique": {"name": "DatabaseConfig_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.FieldConfig": {"name": "FieldConfig", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "databaseCode": {"name": "databaseCode", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "fieldName": {"name": "fieldName", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "displayName": {"name": "displayName", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "fieldType": {"name": "fieldType", "type": "field_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'text'"}, "isVisible": {"name": "isVisible", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "isSearchable": {"name": "isSearchable", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "isFilterable": {"name": "isFilterable", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "isAdvancedSearchable": {"name": "isAdvancedSearchable", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "isSortable": {"name": "isSortable", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "sortOrder": {"name": "sortOrder", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "listOrder": {"name": "listOrder", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "detailOrder": {"name": "detailOrder", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "searchType": {"name": "searchType", "type": "search_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'contains'"}, "filterType": {"name": "filterType", "type": "filter_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'select'"}, "todetail": {"name": "todetail", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "isStatisticsEnabled": {"name": "isStatisticsEnabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "statisticsOrder": {"name": "statisticsOrder", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "statisticsType": {"name": "statisticsType", "type": "statistics_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'count'"}, "statisticsDisplayName": {"name": "statisticsDisplayName", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "statisticsSortOrder": {"name": "statisticsSortOrder", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false, "default": "'desc'"}, "statisticsDefaultLimit": {"name": "statisticsDefaultLimit", "type": "integer", "primaryKey": false, "notNull": true, "default": 5}, "statisticsMaxLimit": {"name": "statisticsMaxLimit", "type": "integer", "primaryKey": false, "notNull": true, "default": 50}, "isExportable": {"name": "isExportable", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "exportOrder": {"name": "exportOrder", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "exportDisplayName": {"name": "exportDisplayName", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "filterOrder": {"name": "filterOrder", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "validationRules": {"name": "validationRules", "type": "json", "primaryKey": false, "notNull": false}, "options": {"name": "options", "type": "json", "primaryKey": false, "notNull": false}, "statisticsConfig": {"name": "statisticsConfig", "type": "json", "primaryKey": false, "notNull": false}}, "indexes": {"fieldconfig_database_field_unique": {"name": "fieldconfig_database_field_unique", "columns": [{"expression": "databaseCode", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "fieldName", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "fieldconfig_database_idx": {"name": "fieldconfig_database_idx", "columns": [{"expression": "databaseCode", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "fieldconfig_active_idx": {"name": "fieldconfig_active_idx", "columns": [{"expression": "isActive", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "fieldconfig_statistics_idx": {"name": "fieldconfig_statistics_idx", "columns": [{"expression": "isStatisticsEnabled", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "fieldconfig_filter_order_idx": {"name": "fieldconfig_filter_order_idx", "columns": [{"expression": "filterOrder", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.SearchAnalytics": {"name": "SearchAnalytics", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "userId": {"name": "userId", "type": "uuid", "primaryKey": false, "notNull": false}, "sessionId": {"name": "sessionId", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "database": {"name": "database", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "searchType": {"name": "searchType", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "searchQuery": {"name": "searchQuery", "type": "text", "primaryKey": false, "notNull": false}, "sortBy": {"name": "sortBy", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "sortOrder": {"name": "sortOrder", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "resultsCount": {"name": "resultsCount", "type": "integer", "primaryKey": false, "notNull": false}, "searchTime": {"name": "searchTime", "type": "integer", "primaryKey": false, "notNull": false}, "ip": {"name": "ip", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "userAgent": {"name": "userAgent", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "searchFields": {"name": "searchFields", "type": "json", "primaryKey": false, "notNull": false}, "filters": {"name": "filters", "type": "json", "primaryKey": false, "notNull": false}}, "indexes": {"search_analytics_created_at_idx": {"name": "search_analytics_created_at_idx", "columns": [{"expression": "createdAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "search_analytics_database_idx": {"name": "search_analytics_database_idx", "columns": [{"expression": "database", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "search_analytics_search_type_idx": {"name": "search_analytics_search_type_idx", "columns": [{"expression": "searchType", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "search_analytics_user_id_idx": {"name": "search_analytics_user_id_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "search_analytics_session_id_idx": {"name": "search_analytics_session_id_idx", "columns": [{"expression": "sessionId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "search_analytics_database_created_at_idx": {"name": "search_analytics_database_created_at_idx", "columns": [{"expression": "database", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "createdAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "search_analytics_search_type_database_idx": {"name": "search_analytics_search_type_database_idx", "columns": [{"expression": "searchType", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "database", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"SearchAnalytics_userId_User_id_fk": {"name": "SearchAnalytics_userId_User_id_fk", "tableFrom": "SearchAnalytics", "tableTo": "User", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.us_class": {"name": "us_class", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "review_panel": {"name": "review_panel", "type": "text", "primaryKey": false, "notNull": false}, "medicalspecialty": {"name": "medicalspecialty", "type": "text", "primaryKey": false, "notNull": false}, "productcode": {"name": "productcode", "type": "text", "primaryKey": false, "notNull": false}, "devicename": {"name": "devicename", "type": "text", "primaryKey": false, "notNull": false}, "deviceclass": {"name": "deviceclass", "type": "text", "primaryKey": false, "notNull": false}, "unclassified_reason": {"name": "unclassified_reason", "type": "text", "primaryKey": false, "notNull": false}, "gmpexemptflag": {"name": "gmpexemptflag", "type": "text", "primaryKey": false, "notNull": false}, "thirdpartyflag": {"name": "thirdpartyflag", "type": "text", "primaryKey": false, "notNull": false}, "reviewcode": {"name": "reviewcode", "type": "text", "primaryKey": false, "notNull": false}, "regulationnumber": {"name": "regulationnumber", "type": "text", "primaryKey": false, "notNull": false}, "submission_type_id": {"name": "submission_type_id", "type": "text", "primaryKey": false, "notNull": false}, "definition": {"name": "definition", "type": "text", "primaryKey": false, "notNull": false}, "physicalstate": {"name": "physicalstate", "type": "text", "primaryKey": false, "notNull": false}, "technicalmethod": {"name": "technicalmethod", "type": "text", "primaryKey": false, "notNull": false}, "targetarea": {"name": "targetarea", "type": "text", "primaryKey": false, "notNull": false}, "implant_flag": {"name": "implant_flag", "type": "text", "primaryKey": false, "notNull": false}, "life_sustain_support_flag": {"name": "life_sustain_support_flag", "type": "text", "primaryKey": false, "notNull": false}, "summarymalfunctionreporting": {"name": "summarymalfunctionreporting", "type": "text", "primaryKey": false, "notNull": false}, "source_file": {"name": "source_file", "type": "text", "primaryKey": false, "notNull": false}, "source_time": {"name": "source_time", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"us_class_productcode_idx": {"name": "us_class_productcode_idx", "columns": [{"expression": "productcode", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "us_class_devicename_idx": {"name": "us_class_devicename_idx", "columns": [{"expression": "devicename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "us_class_deviceclass_idx": {"name": "us_class_deviceclass_idx", "columns": [{"expression": "deviceclass", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "us_class_medicalspecialty_idx": {"name": "us_class_medicalspecialty_idx", "columns": [{"expression": "medicalspecialty", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "us_class_regulationnumber_idx": {"name": "us_class_regulationnumber_idx", "columns": [{"expression": "regulationnumber", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.us_pmn": {"name": "us_pmn", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "knumber": {"name": "knumber", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "applicant": {"name": "applicant", "type": "text", "primaryKey": false, "notNull": false}, "contact": {"name": "contact", "type": "text", "primaryKey": false, "notNull": false}, "street1": {"name": "street1", "type": "text", "primaryKey": false, "notNull": false}, "street2": {"name": "street2", "type": "text", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "country_code": {"name": "country_code", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "zip": {"name": "zip", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "postal_code": {"name": "postal_code", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "datereceived": {"name": "datereceived", "type": "timestamp", "primaryKey": false, "notNull": false}, "decisiondate": {"name": "decisiondate", "type": "timestamp", "primaryKey": false, "notNull": false}, "decision": {"name": "decision", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "reviewadvisecomm": {"name": "reviewadvisecomm", "type": "text", "primaryKey": false, "notNull": false}, "productcode": {"name": "productcode", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "stateorsumm": {"name": "stateorsumm", "type": "text", "primaryKey": false, "notNull": false}, "classadvisecomm": {"name": "classadvisecomm", "type": "text", "primaryKey": false, "notNull": false}, "sspindicator": {"name": "sspindicator", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "thirdparty": {"name": "thirdparty", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "expeditedreview": {"name": "expeditedreview", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "devicename": {"name": "devicename", "type": "text", "primaryKey": false, "notNull": false}, "source_file": {"name": "source_file", "type": "text", "primaryKey": false, "notNull": false}, "source_time": {"name": "source_time", "type": "text", "primaryKey": false, "notNull": false}, "decision_year": {"name": "decision_year", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"us_pmn_knumber_idx": {"name": "us_pmn_knumber_idx", "columns": [{"expression": "knumber", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "us_pmn_applicant_idx": {"name": "us_pmn_applicant_idx", "columns": [{"expression": "applicant", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "us_pmn_decisiondate_idx": {"name": "us_pmn_decisiondate_idx", "columns": [{"expression": "decisiondate", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "us_pmn_decision_idx": {"name": "us_pmn_decision_idx", "columns": [{"expression": "decision", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "us_pmn_productcode_idx": {"name": "us_pmn_productcode_idx", "columns": [{"expression": "productcode", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "us_pmn_devicename_idx": {"name": "us_pmn_devicename_idx", "columns": [{"expression": "devicename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "us_pmn_decision_year_idx": {"name": "us_pmn_decision_year_idx", "columns": [{"expression": "decision_year", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.UserSession": {"name": "UserSession", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "userId": {"name": "userId", "type": "uuid", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "expiresAt": {"name": "expiresAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"UserSession_userId_User_id_fk": {"name": "UserSession_userId_User_id_fk", "tableFrom": "UserSession", "tableTo": "User", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"UserSession_token_unique": {"name": "UserSession_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.User": {"name": "User", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "membershipType": {"name": "membershipType", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'free'"}, "membershipExpiry": {"name": "membershipExpiry", "type": "timestamp", "primaryKey": false, "notNull": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "emailVerified": {"name": "emailVerified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"User_email_unique": {"name": "User_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.field_type": {"name": "field_type", "schema": "public", "values": ["text", "date", "number", "boolean", "select", "json"]}, "public.filter_type": {"name": "filter_type", "schema": "public", "values": ["select", "input", "date_range", "checkbox", "multi_select", "range"]}, "public.search_type": {"name": "search_type", "schema": "public", "values": ["exact", "contains", "range", "date_range", "starts_with", "ends_with"]}, "public.statistics_type": {"name": "statistics_type", "schema": "public", "values": ["count", "sum", "avg", "min_max", "group_by"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}