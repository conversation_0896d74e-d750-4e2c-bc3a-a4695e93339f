CREATE TYPE "public"."field_type" AS ENUM('text', 'date', 'number', 'boolean', 'select', 'json');--> statement-breakpoint
CREATE TYPE "public"."filter_type" AS ENUM('select', 'input', 'date_range', 'checkbox', 'multi_select', 'range');--> statement-breakpoint
CREATE TYPE "public"."search_type" AS ENUM('exact', 'contains', 'range', 'date_range', 'starts_with', 'ends_with');--> statement-breakpoint
CREATE TYPE "public"."statistics_type" AS ENUM('count', 'sum', 'avg', 'min_max', 'group_by');--> statement-breakpoint
CREATE TABLE "ActivityLog" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"userId" uuid,
	"ip" varchar(50) NOT NULL,
	"userAgent" varchar(500),
	"path" varchar(500) NOT NULL,
	"method" varchar(20) NOT NULL,
	"queryParams" text,
	"referer" varchar(500),
	"database" varchar(50),
	"eventType" varchar(50),
	"sessionId" varchar(100),
	"createdAt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "BlockedIp" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"ip" varchar(50) NOT NULL,
	"reason" text,
	"expiresAt" timestamp NOT NULL,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "BlockedIp_ip_unique" UNIQUE("ip")
);
--> statement-breakpoint
CREATE TABLE "Company" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"companyName" text NOT NULL,
	"companyCode" varchar(50),
	"companyShortName" text,
	"region" varchar(50) NOT NULL,
	"province" text,
	"city" text,
	"address" text,
	"phone" text,
	"email" text,
	"website" text,
	"establishDate" timestamp,
	"registeredCapital" text,
	"legalRepresentative" text,
	"businessScope" text,
	"companyType" text,
	"industryCategory" text,
	"isListed" boolean DEFAULT false,
	"stockCode" text,
	"notes" text,
	"database" varchar(50) NOT NULL,
	"businessKey" varchar(200) NOT NULL,
	"businessKeyHash" varchar(255),
	"dataVersion" integer DEFAULT 1 NOT NULL,
	"isActive" boolean DEFAULT true NOT NULL,
	"importedAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "Company_businessKey_unique" UNIQUE("businessKey"),
	CONSTRAINT "Company_businessKeyHash_unique" UNIQUE("businessKeyHash")
);
--> statement-breakpoint
CREATE TABLE "ContactSubmission" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(100) NOT NULL,
	"email" varchar(255) NOT NULL,
	"subject" varchar(200) NOT NULL,
	"category" varchar(50) NOT NULL,
	"message" text NOT NULL,
	"ip" varchar(45),
	"userAgent" varchar(500),
	"status" varchar(20) DEFAULT 'pending' NOT NULL,
	"adminNotes" text,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "DataChangeLog" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"businessKey" varchar(200) NOT NULL,
	"businessKeyHash" varchar(255),
	"operation" varchar(20) NOT NULL,
	"changeReason" varchar(500),
	"importedBy" varchar(100),
	"importedFrom" varchar(200),
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"oldData" json,
	"newData" json
);
--> statement-breakpoint
CREATE TABLE "DatabaseConfig" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"code" varchar(50) NOT NULL,
	"name" varchar(100) NOT NULL,
	"category" varchar(50) NOT NULL,
	"description" text,
	"accessLevel" varchar(20) DEFAULT 'free' NOT NULL,
	"tableName" varchar(100),
	"modelName" varchar(100),
	"maxExportLimit" integer DEFAULT 10000 NOT NULL,
	"defaultExportLimit" integer DEFAULT 1000 NOT NULL,
	"isActive" boolean DEFAULT true NOT NULL,
	"sortOrder" integer NOT NULL,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL,
	"defaultSort" json,
	"exportConfig" json,
	CONSTRAINT "DatabaseConfig_code_unique" UNIQUE("code")
);
--> statement-breakpoint
CREATE TABLE "FieldConfig" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"databaseCode" varchar(50) NOT NULL,
	"fieldName" varchar(100) NOT NULL,
	"displayName" varchar(100) NOT NULL,
	"fieldType" "field_type" DEFAULT 'text' NOT NULL,
	"isVisible" boolean DEFAULT true NOT NULL,
	"isSearchable" boolean DEFAULT false NOT NULL,
	"isFilterable" boolean DEFAULT false NOT NULL,
	"isAdvancedSearchable" boolean DEFAULT false NOT NULL,
	"isSortable" boolean DEFAULT false NOT NULL,
	"sortOrder" integer DEFAULT 0 NOT NULL,
	"listOrder" integer DEFAULT 0 NOT NULL,
	"detailOrder" integer DEFAULT 0 NOT NULL,
	"searchType" "search_type" DEFAULT 'contains' NOT NULL,
	"filterType" "filter_type" DEFAULT 'select' NOT NULL,
	"todetail" boolean DEFAULT false NOT NULL,
	"isStatisticsEnabled" boolean DEFAULT false NOT NULL,
	"statisticsOrder" integer DEFAULT 0 NOT NULL,
	"statisticsType" "statistics_type" DEFAULT 'count' NOT NULL,
	"statisticsDisplayName" varchar(100),
	"statisticsSortOrder" varchar(10) DEFAULT 'desc',
	"statisticsDefaultLimit" integer DEFAULT 5 NOT NULL,
	"statisticsMaxLimit" integer DEFAULT 50 NOT NULL,
	"isExportable" boolean DEFAULT true NOT NULL,
	"exportOrder" integer DEFAULT 0 NOT NULL,
	"exportDisplayName" varchar(100),
	"filterOrder" integer DEFAULT 0 NOT NULL,
	"isActive" boolean DEFAULT true NOT NULL,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL,
	"validationRules" json,
	"options" json,
	"statisticsConfig" json
);
--> statement-breakpoint
CREATE TABLE "SearchAnalytics" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"userId" uuid,
	"sessionId" varchar(100),
	"database" varchar(50) NOT NULL,
	"searchType" varchar(20) NOT NULL,
	"searchQuery" text,
	"sortBy" varchar(50),
	"sortOrder" varchar(10),
	"resultsCount" integer,
	"searchTime" integer,
	"ip" varchar(50) NOT NULL,
	"userAgent" varchar(500),
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"searchFields" json,
	"filters" json
);
--> statement-breakpoint
CREATE TABLE "us_class" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"review_panel" text,
	"medicalspecialty" text,
	"productcode" text,
	"devicename" text,
	"deviceclass" text,
	"unclassified_reason" text,
	"gmpexemptflag" text,
	"thirdpartyflag" text,
	"reviewcode" text,
	"regulationnumber" text,
	"submission_type_id" text,
	"definition" text,
	"physicalstate" text,
	"technicalmethod" text,
	"targetarea" text,
	"implant_flag" text,
	"life_sustain_support_flag" text,
	"summarymalfunctionreporting" text,
	"source_file" text,
	"source_time" text
);
--> statement-breakpoint
CREATE TABLE "us_pmn" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"knumber" varchar(20),
	"applicant" text,
	"contact" text,
	"street1" text,
	"street2" text,
	"city" varchar(100),
	"state" varchar(50),
	"country_code" varchar(10),
	"zip" varchar(20),
	"postal_code" varchar(20),
	"datereceived" timestamp,
	"decisiondate" timestamp,
	"decision" varchar(100),
	"reviewadvisecomm" text,
	"productcode" varchar(20),
	"stateorsumm" text,
	"classadvisecomm" text,
	"sspindicator" varchar(10),
	"type" varchar(50),
	"thirdparty" varchar(10),
	"expeditedreview" varchar(10),
	"devicename" text,
	"source_file" text,
	"source_time" text,
	"decision_year" text
);
--> statement-breakpoint
CREATE TABLE "UserSession" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"userId" uuid NOT NULL,
	"token" varchar(255) NOT NULL,
	"expiresAt" timestamp NOT NULL,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "UserSession_token_unique" UNIQUE("token")
);
--> statement-breakpoint
CREATE TABLE "User" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"email" varchar(255) NOT NULL,
	"password" text NOT NULL,
	"name" varchar(100) NOT NULL,
	"membershipType" varchar(20) DEFAULT 'free' NOT NULL,
	"membershipExpiry" timestamp,
	"isActive" boolean DEFAULT true NOT NULL,
	"emailVerified" boolean DEFAULT false NOT NULL,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "User_email_unique" UNIQUE("email")
);
--> statement-breakpoint
ALTER TABLE "ActivityLog" ADD CONSTRAINT "ActivityLog_userId_User_id_fk" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "SearchAnalytics" ADD CONSTRAINT "SearchAnalytics_userId_User_id_fk" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "UserSession" ADD CONSTRAINT "UserSession_userId_User_id_fk" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "activity_log_created_at_idx" ON "ActivityLog" USING btree ("createdAt");--> statement-breakpoint
CREATE INDEX "activity_log_event_type_idx" ON "ActivityLog" USING btree ("eventType");--> statement-breakpoint
CREATE INDEX "activity_log_database_idx" ON "ActivityLog" USING btree ("database");--> statement-breakpoint
CREATE INDEX "activity_log_session_id_idx" ON "ActivityLog" USING btree ("sessionId");--> statement-breakpoint
CREATE INDEX "activity_log_ip_idx" ON "ActivityLog" USING btree ("ip");--> statement-breakpoint
CREATE INDEX "activity_log_user_id_idx" ON "ActivityLog" USING btree ("userId");--> statement-breakpoint
CREATE INDEX "activity_log_created_at_event_type_idx" ON "ActivityLog" USING btree ("createdAt","eventType");--> statement-breakpoint
CREATE INDEX "activity_log_created_at_database_idx" ON "ActivityLog" USING btree ("createdAt","database");--> statement-breakpoint
CREATE INDEX "activity_log_session_id_event_type_idx" ON "ActivityLog" USING btree ("sessionId","eventType");--> statement-breakpoint
CREATE INDEX "company_database_idx" ON "Company" USING btree ("database");--> statement-breakpoint
CREATE INDEX "company_business_key_idx" ON "Company" USING btree ("businessKey");--> statement-breakpoint
CREATE INDEX "company_business_key_hash_idx" ON "Company" USING btree ("businessKeyHash");--> statement-breakpoint
CREATE INDEX "contact_submission_email_idx" ON "ContactSubmission" USING btree ("email");--> statement-breakpoint
CREATE INDEX "contact_submission_category_idx" ON "ContactSubmission" USING btree ("category");--> statement-breakpoint
CREATE INDEX "contact_submission_status_idx" ON "ContactSubmission" USING btree ("status");--> statement-breakpoint
CREATE INDEX "contact_submission_created_at_idx" ON "ContactSubmission" USING btree ("createdAt");--> statement-breakpoint
CREATE INDEX "data_change_log_business_key_idx" ON "DataChangeLog" USING btree ("businessKey");--> statement-breakpoint
CREATE INDEX "data_change_log_business_key_hash_idx" ON "DataChangeLog" USING btree ("businessKeyHash");--> statement-breakpoint
CREATE INDEX "data_change_log_operation_idx" ON "DataChangeLog" USING btree ("operation");--> statement-breakpoint
CREATE INDEX "data_change_log_created_at_idx" ON "DataChangeLog" USING btree ("createdAt");--> statement-breakpoint
CREATE UNIQUE INDEX "fieldconfig_database_field_unique" ON "FieldConfig" USING btree ("databaseCode","fieldName");--> statement-breakpoint
CREATE INDEX "fieldconfig_database_idx" ON "FieldConfig" USING btree ("databaseCode");--> statement-breakpoint
CREATE INDEX "fieldconfig_active_idx" ON "FieldConfig" USING btree ("isActive");--> statement-breakpoint
CREATE INDEX "fieldconfig_statistics_idx" ON "FieldConfig" USING btree ("isStatisticsEnabled");--> statement-breakpoint
CREATE INDEX "fieldconfig_filter_order_idx" ON "FieldConfig" USING btree ("filterOrder");--> statement-breakpoint
CREATE INDEX "search_analytics_created_at_idx" ON "SearchAnalytics" USING btree ("createdAt");--> statement-breakpoint
CREATE INDEX "search_analytics_database_idx" ON "SearchAnalytics" USING btree ("database");--> statement-breakpoint
CREATE INDEX "search_analytics_search_type_idx" ON "SearchAnalytics" USING btree ("searchType");--> statement-breakpoint
CREATE INDEX "search_analytics_user_id_idx" ON "SearchAnalytics" USING btree ("userId");--> statement-breakpoint
CREATE INDEX "search_analytics_session_id_idx" ON "SearchAnalytics" USING btree ("sessionId");--> statement-breakpoint
CREATE INDEX "search_analytics_database_created_at_idx" ON "SearchAnalytics" USING btree ("database","createdAt");--> statement-breakpoint
CREATE INDEX "search_analytics_search_type_database_idx" ON "SearchAnalytics" USING btree ("searchType","database");--> statement-breakpoint
CREATE INDEX "us_class_productcode_idx" ON "us_class" USING btree ("productcode");--> statement-breakpoint
CREATE INDEX "us_class_devicename_idx" ON "us_class" USING btree ("devicename");--> statement-breakpoint
CREATE INDEX "us_class_deviceclass_idx" ON "us_class" USING btree ("deviceclass");--> statement-breakpoint
CREATE INDEX "us_class_medicalspecialty_idx" ON "us_class" USING btree ("medicalspecialty");--> statement-breakpoint
CREATE INDEX "us_class_regulationnumber_idx" ON "us_class" USING btree ("regulationnumber");--> statement-breakpoint
CREATE INDEX "us_pmn_knumber_idx" ON "us_pmn" USING btree ("knumber");--> statement-breakpoint
CREATE INDEX "us_pmn_applicant_idx" ON "us_pmn" USING btree ("applicant");--> statement-breakpoint
CREATE INDEX "us_pmn_decisiondate_idx" ON "us_pmn" USING btree ("decisiondate");--> statement-breakpoint
CREATE INDEX "us_pmn_decision_idx" ON "us_pmn" USING btree ("decision");--> statement-breakpoint
CREATE INDEX "us_pmn_productcode_idx" ON "us_pmn" USING btree ("productcode");--> statement-breakpoint
CREATE INDEX "us_pmn_devicename_idx" ON "us_pmn" USING btree ("devicename");--> statement-breakpoint
CREATE INDEX "us_pmn_decision_year_idx" ON "us_pmn" USING btree ("decision_year");