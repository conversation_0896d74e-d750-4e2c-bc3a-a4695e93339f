-- Migration: Add Enhanced Configuration Fields for Drizzle Optimization
-- This migration adds new fields to support enhanced Drizzle ORM functionality
-- while keeping the existing modelName field for backward compatibility

-- Add new enhanced fields to DatabaseConfig table
ALTER TABLE "DatabaseConfig" ADD COLUMN "schemaName" varchar(100) DEFAULT 'public';
ALTER TABLE "DatabaseConfig" ADD COLUMN "connectionPool" varchar(50) DEFAULT 'default';
ALTER TABLE "DatabaseConfig" ADD COLUMN "queryTimeout" integer DEFAULT 30000;
ALTER TABLE "DatabaseConfig" ADD COLUMN "cacheStrategy" varchar(50) DEFAULT 'redis';
ALTER TABLE "DatabaseConfig" ADD COLUMN "cacheTTL" integer DEFAULT 300;
ALTER TABLE "DatabaseConfig" ADD COLUMN "exportFormats" json DEFAULT '["csv", "xlsx"]';
ALTER TABLE "DatabaseConfig" ADD COLUMN "indexStrategy" json;

-- Update existing records with default values
UPDATE "DatabaseConfig" SET 
  "schemaName" = 'public',
  "connectionPool" = 'default',
  "queryTimeout" = 30000,
  "cacheStrategy" = 'redis',
  "cacheTTL" = 300,
  "exportFormats" = '["csv", "xlsx"]'
WHERE "schemaName" IS NULL;

-- Add comments for documentation
COMMENT ON COLUMN "DatabaseConfig"."schemaName" IS 'PostgreSQL schema name (default: public)';
COMMENT ON COLUMN "DatabaseConfig"."connectionPool" IS 'Connection pool identifier for multi-database support';
COMMENT ON COLUMN "DatabaseConfig"."queryTimeout" IS 'Query timeout in milliseconds';
COMMENT ON COLUMN "DatabaseConfig"."cacheStrategy" IS 'Caching strategy: redis, memory, or none';
COMMENT ON COLUMN "DatabaseConfig"."cacheTTL" IS 'Cache time-to-live in seconds';
COMMENT ON COLUMN "DatabaseConfig"."exportFormats" IS 'Supported export formats as JSON array';
COMMENT ON COLUMN "DatabaseConfig"."indexStrategy" IS 'Index optimization strategy configuration';

-- Create indexes for better performance on new fields
CREATE INDEX IF NOT EXISTS "idx_database_config_schema_name" ON "DatabaseConfig"("schemaName");
CREATE INDEX IF NOT EXISTS "idx_database_config_connection_pool" ON "DatabaseConfig"("connectionPool");
CREATE INDEX IF NOT EXISTS "idx_database_config_cache_strategy" ON "DatabaseConfig"("cacheStrategy");
