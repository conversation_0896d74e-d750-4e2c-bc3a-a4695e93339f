#!/usr/bin/env tsx

import { config } from 'dotenv';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { eq, like, count } from 'drizzle-orm';

// 加载环境变量
config({ path: '.env.local' });

// 简化的表定义，直接映射到现有数据库结构
import { pgTable, uuid, varchar, text, boolean, timestamp, integer } from 'drizzle-orm/pg-core';

const databaseConfigs = pgTable('DatabaseConfig', {
  id: uuid('id').primaryKey(),
  code: varchar('code', { length: 50 }).unique().notNull(),
  name: varchar('name', { length: 100 }).notNull(),
  category: varchar('category', { length: 50 }).notNull(),
  description: text('description'),
  accessLevel: varchar('accessLevel', { length: 20 }).default('free').notNull(),
  isActive: boolean('isActive').default(true).notNull(),
  sortOrder: integer('sortOrder').notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().notNull(),
});

const usPremarketNotifications = pgTable('us_pmn', {
  id: uuid('id').primaryKey(),
  knumber: varchar('knumber', { length: 20 }),
  applicant: text('applicant'),
  devicename: text('devicename'),
  decision: varchar('decision', { length: 100 }),
  decisiondate: timestamp('decisiondate'),
  productcode: varchar('productcode', { length: 20 }),
});

/**
 * 测试 Drizzle 的完整功能
 */
async function testDrizzleFunctionality() {
  console.log('🚀 测试 Drizzle 完整功能...');

  const connectionString = process.env.DATABASE_URL!;
  const connection = postgres(connectionString, {
    max: 5,
    idle_timeout: 20,
    connect_timeout: 10,
  });

  const db = drizzle(connection, {
    schema: { databaseConfigs, usPremarketNotifications }
  });

  try {
    // 测试1: 基本查询
    console.log('\n📋 测试1: 查询数据库配置...');
    const configs = await db.select().from(databaseConfigs).limit(5);
    console.log(`✅ 找到 ${configs.length} 个配置:`);
    configs.forEach((config, index) => {
      console.log(`  ${index + 1}. ${config.name} (${config.code}) - ${config.category}`);
    });

    // 测试2: 条件查询
    console.log('\n🔍 测试2: 条件查询...');
    const activeConfigs = await db
      .select()
      .from(databaseConfigs)
      .where(eq(databaseConfigs.isActive, true));
    console.log(`✅ 找到 ${activeConfigs.length} 个活跃配置`);

    // 测试3: 模糊查询
    console.log('\n🔎 测试3: 模糊查询...');
    const usConfigs = await db
      .select()
      .from(databaseConfigs)
      .where(like(databaseConfigs.name, '%US%'));
    console.log(`✅ 找到 ${usConfigs.length} 个包含 'US' 的配置`);

    // 测试4: 计数查询
    console.log('\n🔢 测试4: 计数查询...');
    const totalConfigs = await db
      .select({ count: count() })
      .from(databaseConfigs);
    console.log(`✅ 总配置数量: ${totalConfigs[0].count}`);

    // 测试5: 查询 US PMN 数据
    console.log('\n🏥 测试5: 查询 US PMN 数据...');
    const pmnData = await db
      .select({
        knumber: usPremarketNotifications.knumber,
        applicant: usPremarketNotifications.applicant,
        devicename: usPremarketNotifications.devicename,
        decision: usPremarketNotifications.decision,
      })
      .from(usPremarketNotifications)
      .limit(3);
    
    console.log(`✅ 找到 ${pmnData.length} 条 PMN 记录:`);
    pmnData.forEach((pmn, index) => {
      console.log(`  ${index + 1}. ${pmn.knumber} - ${pmn.applicant}`);
      console.log(`     设备: ${pmn.devicename?.substring(0, 50)}...`);
      console.log(`     决定: ${pmn.decision}`);
    });

    // 测试6: 复杂查询 - 带排序
    console.log('\n📊 测试6: 复杂查询 - 带排序...');
    const sortedConfigs = await db
      .select({
        code: databaseConfigs.code,
        name: databaseConfigs.name,
        sortOrder: databaseConfigs.sortOrder,
      })
      .from(databaseConfigs)
      .orderBy(databaseConfigs.sortOrder);
    
    console.log(`✅ 按排序顺序的配置:`);
    sortedConfigs.forEach((config, index) => {
      console.log(`  ${index + 1}. [${config.sortOrder}] ${config.name} (${config.code})`);
    });

    await connection.end();
    console.log('\n🎉 所有 Drizzle 功能测试通过！');
    console.log('\n✅ 迁移验证结果:');
    console.log('  - 数据库连接: ✅ 正常');
    console.log('  - 基本查询: ✅ 正常');
    console.log('  - 条件查询: ✅ 正常');
    console.log('  - 模糊查询: ✅ 正常');
    console.log('  - 计数查询: ✅ 正常');
    console.log('  - 复杂查询: ✅ 正常');
    console.log('  - 数据完整性: ✅ 保持');

  } catch (error) {
    console.error('❌ Drizzle 功能测试失败:', error);
    
    if (error instanceof Error) {
      console.error('错误详情:', error.message);
      console.error('错误堆栈:', error.stack);
    }
    
    await connection.end();
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  testDrizzleFunctionality().catch(error => {
    console.error('功能测试脚本执行失败:', error);
    process.exit(1);
  });
}
