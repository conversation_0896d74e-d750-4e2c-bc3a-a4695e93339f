#!/usr/bin/env tsx

/**
 * 简单的数据库连接和表结构检查脚本
 */

import { config } from 'dotenv';
import postgres from 'postgres';
import { drizzle } from 'drizzle-orm/postgres-js';
import { sql } from 'drizzle-orm';

// 加载环境变量
config({ path: '.env.local' });

interface ColumnInfo {
  column_name: string;
  data_type: string;
  is_nullable: string;
  column_default: string | null;
}

async function checkDatabase() {
  console.log('🚀 开始数据库检查...\n');

  const connectionString = process.env.DATABASE_URL;
  if (!connectionString) {
    console.error('❌ DATABASE_URL 环境变量未设置');
    console.log('当前环境变量:');
    console.log('DATABASE_URL:', process.env.DATABASE_URL);
    return false;
  }

  console.log('📡 连接字符串:', connectionString.replace(/:[^:@]*@/, ':***@'));

  let connection: postgres.Sql;
  let db: ReturnType<typeof drizzle>;

  try {
    // 创建连接
    connection = postgres(connectionString, {
      max: 5,
      idle_timeout: 20,
      connect_timeout: 10,
    });

    db = drizzle(connection);

    // 测试连接
    console.log('🔍 测试数据库连接...');
    await db.execute(sql`SELECT 1 as test`);
    console.log('✅ 数据库连接成功\n');

    // 检查 DatabaseConfig 表是否存在
    console.log('📋 检查 DatabaseConfig 表...');
    const tableExists = await db.execute(sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'DatabaseConfig'
      ) as exists
    `);

    if (!(tableExists[0] as any).exists) {
      console.log('❌ DatabaseConfig 表不存在');
      console.log('请先运行数据库迁移: npx drizzle-kit push');
      return false;
    }

    console.log('✅ DatabaseConfig 表存在');

    // 获取表结构
    console.log('\n📊 获取表结构信息...');
    const columns = await db.execute(sql`
      SELECT 
        column_name,
        data_type,
        is_nullable,
        column_default
      FROM information_schema.columns 
      WHERE table_name = 'DatabaseConfig' 
      AND table_schema = 'public'
      ORDER BY ordinal_position
    `) as ColumnInfo[];

    console.log(`✅ 找到 ${columns.length} 个字段\n`);

    // 检查新增字段
    const expectedNewFields = [
      'schemaName',
      'connectionPool', 
      'queryTimeout',
      'cacheStrategy',
      'cacheTTL',
      'exportFormats',
      'indexStrategy'
    ];

    const columnNames = columns.map(col => col.column_name);
    const existing = expectedNewFields.filter(field => columnNames.includes(field));
    const missing = expectedNewFields.filter(field => !columnNames.includes(field));

    console.log('🔍 检查增强字段:');
    console.log(`✅ 已存在的新字段 (${existing.length}/${expectedNewFields.length}):`, existing.join(', '));
    
    if (missing.length > 0) {
      console.log(`⚠️ 缺失的字段 (${missing.length}):`, missing.join(', '));
      console.log('\n🚨 需要运行数据库迁移！');
      console.log('请运行: npx drizzle-kit push');
      console.log('或手动执行: drizzle/0001_add_enhanced_config_fields.sql');
    } else {
      console.log('🎉 所有增强字段都已存在！');
    }

    // 显示表结构
    console.log('\n📋 DatabaseConfig 表结构:');
    console.log('═'.repeat(80));
    console.log('字段名'.padEnd(20) + '数据类型'.padEnd(15) + '可空'.padEnd(8) + '默认值');
    console.log('─'.repeat(80));
    
    columns.forEach(col => {
      const name = col.column_name.padEnd(20);
      const type = col.data_type.padEnd(15);
      const nullable = (col.is_nullable === 'YES' ? '是' : '否').padEnd(8);
      const defaultVal = col.column_default || 'N/A';
      console.log(`${name}${type}${nullable}${defaultVal}`);
    });
    
    console.log('═'.repeat(80));

    // 检查现有数据
    console.log('\n📊 检查现有配置数据...');
    const configCount = await db.execute(sql`
      SELECT COUNT(*) as count FROM "DatabaseConfig"
    `);

    const count = (configCount[0] as any).count;
    console.log(`✅ 找到 ${count} 个配置记录`);

    if (count > 0) {
      const sampleConfigs = await db.execute(sql`
        SELECT code, name, "tableName", "isActive" 
        FROM "DatabaseConfig" 
        LIMIT 3
      `);

      console.log('\n📋 示例配置:');
      (sampleConfigs as any[]).forEach((config, index) => {
        console.log(`  ${index + 1}. ${config.name} (${config.code})`);
        console.log(`     表名: ${config.tableName || 'N/A'}`);
        console.log(`     状态: ${config.isActive ? '✅ 活跃' : '❌ 非活跃'}`);
      });
    }

    console.log('\n🎉 数据库检查完成！');
    
    if (missing.length === 0) {
      console.log('✅ 数据库已准备就绪，可以使用增强的 Drizzle 配置系统');
      console.log('\n下一步可以运行:');
      console.log('npx tsx scripts/migrate-to-enhanced-drizzle-config.ts --dry-run');
    }

    return missing.length === 0;

  } catch (error) {
    console.error('❌ 数据库检查失败:', error);
    return false;
  } finally {
    if (connection!) {
      await connection.end();
    }
  }
}

// 主执行
checkDatabase()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('检查失败:', error);
    process.exit(1);
  });
