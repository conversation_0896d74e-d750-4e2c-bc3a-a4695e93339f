#!/usr/bin/env tsx

/**
 * 数据库表结构验证脚本
 *
 * 此脚本用于验证：
 * 1. 数据库连接是否正常
 * 2. DatabaseConfig 表是否存在新增的字段
 * 3. 现有配置数据的完整性
 * 4. 新字段的默认值是否正确设置
 */

import { config } from 'dotenv';
import { db } from '../src/lib/db-server';
import { databaseConfigs } from '../src/db/schema';
import { sql } from 'drizzle-orm';

// 加载环境变量
config({ path: '.env.local' });

interface ColumnInfo {
  column_name: string;
  data_type: string;
  is_nullable: string;
  column_default: string | null;
}

/**
 * 数据库表结构验证器
 */
class DatabaseSchemaVerifier {
  
  /**
   * 验证数据库连接
   */
  async verifyConnection(): Promise<boolean> {
    try {
      console.log('🔍 验证数据库连接...');
      const result = await db.execute(sql`SELECT 1 as test`);
      console.log('✅ 数据库连接正常');
      return true;
    } catch (error) {
      console.error('❌ 数据库连接失败:', error);
      return false;
    }
  }

  /**
   * 获取 DatabaseConfig 表的列信息
   */
  async getTableColumns(): Promise<ColumnInfo[]> {
    try {
      console.log('📋 获取 DatabaseConfig 表结构...');
      
      const columns = await db.execute(sql`
        SELECT 
          column_name,
          data_type,
          is_nullable,
          column_default
        FROM information_schema.columns 
        WHERE table_name = 'DatabaseConfig' 
        AND table_schema = 'public'
        ORDER BY ordinal_position
      `);

      return columns as ColumnInfo[];
    } catch (error) {
      console.error('❌ 获取表结构失败:', error);
      throw error;
    }
  }

  /**
   * 验证新增字段是否存在
   */
  verifyNewFields(columns: ColumnInfo[]): { missing: string[], existing: string[] } {
    console.log('🔍 验证新增字段...');
    
    const expectedNewFields = [
      'schemaName',
      'connectionPool', 
      'queryTimeout',
      'cacheStrategy',
      'cacheTTL',
      'exportFormats',
      'indexStrategy'
    ];

    const columnNames = columns.map(col => col.column_name);
    const existing = expectedNewFields.filter(field => columnNames.includes(field));
    const missing = expectedNewFields.filter(field => !columnNames.includes(field));

    console.log(`✅ 已存在的新字段 (${existing.length}):`, existing.join(', '));
    if (missing.length > 0) {
      console.log(`⚠️ 缺失的字段 (${missing.length}):`, missing.join(', '));
    }

    return { missing, existing };
  }

  /**
   * 验证现有配置数据
   */
  async verifyExistingConfigs(): Promise<void> {
    try {
      console.log('📊 验证现有配置数据...');
      
      const configs = await db
        .select({
          code: databaseConfigs.code,
          name: databaseConfigs.name,
          tableName: databaseConfigs.tableName,
          isActive: databaseConfigs.isActive,
          // 尝试读取新字段
          schemaName: databaseConfigs.schemaName,
          connectionPool: databaseConfigs.connectionPool,
          queryTimeout: databaseConfigs.queryTimeout,
          cacheStrategy: databaseConfigs.cacheStrategy,
          cacheTTL: databaseConfigs.cacheTTL,
        })
        .from(databaseConfigs)
        .limit(5);

      console.log(`✅ 找到 ${configs.length} 个配置记录`);
      
      configs.forEach((config, index) => {
        console.log(`\n  ${index + 1}. ${config.name} (${config.code})`);
        console.log(`     表名: ${config.tableName || 'N/A'}`);
        console.log(`     Schema: ${config.schemaName || 'N/A'}`);
        console.log(`     连接池: ${config.connectionPool || 'N/A'}`);
        console.log(`     查询超时: ${config.queryTimeout || 'N/A'}ms`);
        console.log(`     缓存策略: ${config.cacheStrategy || 'N/A'}`);
        console.log(`     缓存TTL: ${config.cacheTTL || 'N/A'}s`);
        console.log(`     状态: ${config.isActive ? '✅ 活跃' : '❌ 非活跃'}`);
      });

    } catch (error) {
      console.error('❌ 验证配置数据失败:', error);
      throw error;
    }
  }

  /**
   * 检查是否需要运行迁移
   */
  checkMigrationNeeded(missingFields: string[]): boolean {
    if (missingFields.length > 0) {
      console.log('\n🚨 需要运行数据库迁移！');
      console.log('缺失的字段:', missingFields.join(', '));
      console.log('\n请运行以下命令来应用迁移:');
      console.log('1. npx drizzle-kit push');
      console.log('2. 或手动执行: drizzle/0001_add_enhanced_config_fields.sql');
      return true;
    } else {
      console.log('\n✅ 数据库表结构已是最新版本！');
      return false;
    }
  }

  /**
   * 生成表结构报告
   */
  generateReport(columns: ColumnInfo[]): void {
    console.log('\n📋 DatabaseConfig 表结构报告:');
    console.log('═'.repeat(80));
    console.log('字段名'.padEnd(20) + '数据类型'.padEnd(15) + '可空'.padEnd(8) + '默认值');
    console.log('─'.repeat(80));
    
    columns.forEach(col => {
      const name = col.column_name.padEnd(20);
      const type = col.data_type.padEnd(15);
      const nullable = (col.is_nullable === 'YES' ? '是' : '否').padEnd(8);
      const defaultVal = col.column_default || 'N/A';
      console.log(`${name}${type}${nullable}${defaultVal}`);
    });
    
    console.log('═'.repeat(80));
  }

  /**
   * 运行完整验证
   */
  async runFullVerification(): Promise<boolean> {
    console.log('🚀 开始数据库表结构验证...\n');

    try {
      // 1. 验证连接
      const connectionOk = await this.verifyConnection();
      if (!connectionOk) {
        return false;
      }

      // 2. 获取表结构
      const columns = await this.getTableColumns();
      
      // 3. 生成报告
      this.generateReport(columns);

      // 4. 验证新字段
      const { missing, existing } = this.verifyNewFields(columns);

      // 5. 验证现有数据
      await this.verifyExistingConfigs();

      // 6. 检查是否需要迁移
      const needsMigration = this.checkMigrationNeeded(missing);

      console.log('\n🎉 验证完成！');
      
      if (!needsMigration) {
        console.log('✅ 数据库已准备就绪，可以使用增强的 Drizzle 配置系统');
        console.log('\n下一步可以运行:');
        console.log('npx tsx scripts/migrate-to-enhanced-drizzle-config.ts --dry-run');
      }

      return !needsMigration;

    } catch (error) {
      console.error('\n❌ 验证过程中发生错误:', error);
      return false;
    }
  }
}

/**
 * 主执行函数
 */
async function main() {
  const verifier = new DatabaseSchemaVerifier();
  
  try {
    const success = await verifier.runFullVerification();
    process.exit(success ? 0 : 1);
  } catch (error) {
    console.error('验证失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

export { DatabaseSchemaVerifier };
