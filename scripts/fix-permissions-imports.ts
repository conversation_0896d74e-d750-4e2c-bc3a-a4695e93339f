#!/usr/bin/env tsx

import { readFileSync, writeFileSync, existsSync } from 'fs';

/**
 * 修复权限导入问题
 * 将客户端文件的权限导入改为客户端安全版本
 */
class PermissionsImportFixer {
  
  /**
   * 客户端文件列表（这些文件应该使用 permissions-client）
   */
  private clientFiles = [
    'src/lib/api.ts',
    'src/lib/auth.tsx',
    'src/lib/preloader.ts',
    'src/lib/configLoader.ts',
    'src/app/debug/loading/page.tsx',
    'src/app/debug-permissions/page.tsx',
    'src/app/register/page.tsx',
    'src/app/page.tsx',
    'src/components/AccessRestrictedAlert.tsx',
    'src/components/Navigation.tsx'
  ];

  /**
   * 服务器端文件列表（这些文件可以使用原始 permissions）
   */
  private serverFiles = [
    'src/app/api/debug/permissions/route.ts',
    'src/app/api/export/[database]/route.ts',
    'src/app/api/data/[database]/[id]/route.ts',
    'src/app/data/detail/[database]/[id]/page.tsx'
  ];

  /**
   * 修复单个文件的导入
   */
  fixFileImports(filePath: string, isClientFile: boolean): boolean {
    if (!existsSync(filePath)) {
      console.log(`⚠️  文件不存在: ${filePath}`);
      return false;
    }

    let content = readFileSync(filePath, 'utf-8');
    let modified = false;

    if (isClientFile) {
      // 客户端文件：使用 permissions-client
      const replacements = [
        {
          from: `import { getDatabaseConfigs } from "@/lib/permissions";`,
          to: `import { getDatabaseConfigs } from "@/lib/permissions-client";`
        },
        {
          from: `import { canAccessDatabaseSync, getDatabaseConfigs } from "@/lib/permissions";`,
          to: `import { canAccessDatabaseSync, getDatabaseConfigs } from "@/lib/permissions-client";`
        },
        {
          from: `from './permissions'`,
          to: `from './permissions-client'`
        },
        {
          from: `from "@/lib/permissions"`,
          to: `from "@/lib/permissions-client"`
        }
      ];

      for (const replacement of replacements) {
        if (content.includes(replacement.from)) {
          content = content.replace(new RegExp(replacement.from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), replacement.to);
          modified = true;
          console.log(`✅ ${filePath}: 更新为客户端导入`);
        }
      }
    } else {
      // 服务器端文件：确保使用正确的服务器端导入
      console.log(`📄 ${filePath}: 服务器端文件，保持原有导入`);
    }

    if (modified) {
      writeFileSync(filePath, content);
      return true;
    }

    return false;
  }

  /**
   * 执行所有修复
   */
  fixAllImports(): void {
    console.log('🔧 修复权限导入问题...\n');

    let totalFixed = 0;

    // 修复客户端文件
    console.log('📱 处理客户端文件:');
    for (const file of this.clientFiles) {
      const wasFixed = this.fixFileImports(file, true);
      if (wasFixed) {
        totalFixed++;
      }
    }

    // 检查服务器端文件
    console.log('\n🖥️  检查服务器端文件:');
    for (const file of this.serverFiles) {
      this.fixFileImports(file, false);
    }

    console.log(`\n📊 修复完成: ${totalFixed} 个文件被修改`);
  }

  /**
   * 创建必要的导出到 permissions-client
   */
  createClientExports(): void {
    console.log('\n📝 更新 permissions-client.ts 导出...');

    const clientPermissionsPath = 'src/lib/permissions-client.ts';
    if (!existsSync(clientPermissionsPath)) {
      console.log('❌ permissions-client.ts 不存在');
      return;
    }

    let content = readFileSync(clientPermissionsPath, 'utf-8');

    // 添加缺失的导出
    const additionalExports = `

// 兼容性导出 - 从原始 permissions.ts 迁移
export const DATABASE_CONFIGS: DatabaseConfigClient[] = [];

export type MembershipType = UserPermissionLevel;

export const MEMBERSHIP_BENEFITS = {
  free: {
    name: '免费版',
    databases: ['us_class'],
    features: ['基础搜索', '基础筛选']
  },
  premium: {
    name: '高级版',
    databases: ['us_pmn', 'us_class', 'deviceCN', 'deviceHK'],
    features: ['高级搜索', '数据导出', '详细信息']
  },
  admin: {
    name: '管理员',
    databases: ['*'],
    features: ['所有功能', '用户管理', '系统配置']
  }
};

// 兼容性函数
export function getDatabaseAccessLevel(databaseCode: string): DatabaseAccessLevel {
  const configs = getDatabaseConfigs();
  const config = configs.find(c => c.code === databaseCode);
  return config?.accessLevel || 'free';
}
`;

    if (!content.includes('DATABASE_CONFIGS')) {
      content += additionalExports;
      writeFileSync(clientPermissionsPath, content);
      console.log('✅ 已添加兼容性导出');
    } else {
      console.log('○ 兼容性导出已存在');
    }
  }
}

// 运行修复
if (require.main === module) {
  const fixer = new PermissionsImportFixer();
  fixer.createClientExports();
  fixer.fixAllImports();
}
