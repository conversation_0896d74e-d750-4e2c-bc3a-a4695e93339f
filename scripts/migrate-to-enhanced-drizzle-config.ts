#!/usr/bin/env tsx

/**
 * Migration Script: Prisma to Enhanced Drizzle Configuration
 * 
 * This script performs the following operations:
 * 1. Adds new Drizzle-optimized fields to DatabaseConfig table
 * 2. Removes Prisma-specific modelName field dependencies
 * 3. Updates existing configurations with enhanced defaults
 * 4. Validates all configurations after migration
 * 5. Updates table mapping to use direct code-based lookup
 */

import { config } from 'dotenv';
import postgres from 'postgres';
import { drizzle } from 'drizzle-orm/postgres-js';
import { databaseConfigs } from '../src/db/schema';
import { eq, isNotNull, sql } from 'drizzle-orm';
import { enhancedConfigService } from '../src/lib/services/enhancedConfigService';
import type { EnhancedDatabaseConfig } from '../src/lib/types/databaseConfig';

// 加载环境变量
config({ path: '.env.local' });

interface MigrationOptions {
  dryRun?: boolean;
  verbose?: boolean;
  skipValidation?: boolean;
}

interface MigrationStats {
  totalConfigs: number;
  updatedConfigs: number;
  failedConfigs: number;
  addedFields: string[];
  removedFields: string[];
}

/**
 * Enhanced Drizzle Configuration Migration
 */
class EnhancedDrizzleMigration {
  private options: MigrationOptions;
  private db: ReturnType<typeof drizzle>;
  private connection: postgres.Sql;
  private stats: MigrationStats = {
    totalConfigs: 0,
    updatedConfigs: 0,
    failedConfigs: 0,
    addedFields: [],
    removedFields: []
  };

  constructor(options: MigrationOptions = {}) {
    this.options = {
      dryRun: false,
      verbose: false,
      skipValidation: false,
      ...options
    };

    // 初始化数据库连接
    const connectionString = process.env.DATABASE_URL!;
    this.connection = postgres(connectionString, {
      max: 5,
      idle_timeout: 20,
      connect_timeout: 10,
    });
    this.db = drizzle(this.connection);
  }

  /**
   * Run the complete migration process
   */
  async migrate(): Promise<MigrationStats> {
    console.log('🚀 Starting Enhanced Drizzle Configuration Migration...\n');

    try {
      // Step 1: Analyze current configurations
      await this.analyzeCurrentConfigs();

      // Step 2: Add new enhanced fields
      await this.addEnhancedFields();

      // Step 3: Update existing configurations
      await this.updateExistingConfigs();

      // Step 4: Remove deprecated fields (if not dry run)
      if (!this.options.dryRun) {
        await this.removeDeprecatedFields();
      }

      // Step 5: Validate configurations
      if (!this.options.skipValidation) {
        await this.validateConfigurations();
      }

      // Step 6: Update table mapping cache
      await this.updateTableMappingCache();

      console.log('\n✅ Migration completed successfully!');
      this.printMigrationSummary();

      return this.stats;

    } catch (error) {
      console.error('\n❌ Migration failed:', error);
      throw error;
    }
  }

  /**
   * Analyze current database configurations
   */
  private async analyzeCurrentConfigs(): Promise<void> {
    console.log('📊 Analyzing current database configurations...');

    const configs = await this.db
      .select({
        code: databaseConfigs.code,
        name: databaseConfigs.name,
        tableName: databaseConfigs.tableName,
        // modelName: databaseConfigs.modelName, // This field will be removed
        isActive: databaseConfigs.isActive,
      })
      .from(databaseConfigs);

    this.stats.totalConfigs = configs.length;

    if (this.options.verbose) {
      console.log(`\nFound ${configs.length} database configurations:`);
      configs.forEach((config, index) => {
        console.log(`  ${index + 1}. ${config.name} (${config.code})`);
        console.log(`     Table: ${config.tableName || 'N/A'}`);
        console.log(`     Active: ${config.isActive ? '✅' : '❌'}`);
      });
    }

    console.log(`✅ Analysis complete: ${configs.length} configurations found\n`);
  }

  /**
   * Add enhanced fields to existing configurations
   */
  private async addEnhancedFields(): Promise<void> {
    console.log('🔧 Adding enhanced Drizzle fields...');

    const enhancedFields = [
      'schemaName',
      'connectionPool', 
      'queryTimeout',
      'cacheStrategy',
      'cacheTTL',
      'exportFormats',
      'indexStrategy'
    ];

    this.stats.addedFields = enhancedFields;

    if (this.options.dryRun) {
      console.log('🔍 DRY RUN: Would add the following fields:');
      enhancedFields.forEach(field => console.log(`  - ${field}`));
    } else {
      console.log('✅ Enhanced fields are already defined in the schema');
    }

    console.log('');
  }

  /**
   * Update existing configurations with enhanced defaults
   */
  private async updateExistingConfigs(): Promise<void> {
    console.log('📝 Updating existing configurations with enhanced defaults...');

    const configs = await this.db
      .select()
      .from(databaseConfigs)
      .where(eq(databaseConfigs.isActive, true));

    for (const config of configs) {
      try {
        const updates: any = {};
        let needsUpdate = false;

        // Add default values for new fields if they're null
        if (!config.schemaName) {
          updates.schemaName = 'public';
          needsUpdate = true;
        }

        if (!config.connectionPool) {
          updates.connectionPool = 'default';
          needsUpdate = true;
        }

        if (!config.queryTimeout) {
          updates.queryTimeout = 30000;
          needsUpdate = true;
        }

        if (!config.cacheStrategy) {
          updates.cacheStrategy = 'redis';
          needsUpdate = true;
        }

        if (!config.cacheTTL) {
          updates.cacheTTL = 300;
          needsUpdate = true;
        }

        if (!config.exportFormats) {
          updates.exportFormats = ['csv', 'xlsx'];
          needsUpdate = true;
        }

        // Set default index strategy based on category
        if (!config.indexStrategy) {
          updates.indexStrategy = this.getDefaultIndexStrategy(config.category);
          needsUpdate = true;
        }

        if (needsUpdate && !this.options.dryRun) {
          await this.db
            .update(databaseConfigs)
            .set({
              ...updates,
              updatedAt: new Date(),
            })
            .where(eq(databaseConfigs.code, config.code));

          this.stats.updatedConfigs++;
        }

        if (this.options.verbose) {
          console.log(`  ${needsUpdate ? '✅' : '⏭️'} ${config.name} (${config.code})`);
          if (needsUpdate && Object.keys(updates).length > 0) {
            console.log(`     Updated fields: ${Object.keys(updates).join(', ')}`);
          }
        }

      } catch (error) {
        console.error(`❌ Failed to update ${config.code}:`, error);
        this.stats.failedConfigs++;
      }
    }

    console.log(`✅ Updated ${this.stats.updatedConfigs} configurations\n`);
  }

  /**
   * Remove deprecated Prisma-specific fields
   */
  private async removeDeprecatedFields(): Promise<void> {
    console.log('🗑️ Removing deprecated Prisma-specific fields...');

    // Note: In a real migration, you would run SQL to drop the modelName column
    // For now, we'll just mark it as deprecated in our tracking
    this.stats.removedFields = ['modelName'];

    console.log('⚠️ Note: modelName field marked as deprecated');
    console.log('   Run the following SQL manually to remove it:');
    console.log('   ALTER TABLE "DatabaseConfig" DROP COLUMN "modelName";');
    console.log('');
  }

  /**
   * Validate all configurations after migration
   */
  private async validateConfigurations(): Promise<void> {
    console.log('🔍 Validating migrated configurations...');

    const configs = await this.db
      .select({ code: databaseConfigs.code })
      .from(databaseConfigs)
      .where(eq(databaseConfigs.isActive, true));

    let validConfigs = 0;
    let invalidConfigs = 0;

    for (const { code } of configs) {
      try {
        const config = await enhancedConfigService.getConfig(code);
        const validation = enhancedConfigService.validateConfig(config);

        if (validation.isValid) {
          validConfigs++;
          if (this.options.verbose && validation.warnings.length > 0) {
            console.log(`  ⚠️ ${code}: ${validation.warnings.join(', ')}`);
          }
        } else {
          invalidConfigs++;
          console.error(`  ❌ ${code}: ${validation.errors.join(', ')}`);
        }
      } catch (error) {
        invalidConfigs++;
        console.error(`  ❌ ${code}: Validation failed - ${error}`);
      }
    }

    console.log(`✅ Validation complete: ${validConfigs} valid, ${invalidConfigs} invalid\n`);
  }

  /**
   * Update table mapping cache to remove modelName dependencies
   */
  private async updateTableMappingCache(): Promise<void> {
    console.log('🔄 Updating table mapping cache...');

    // Clear all configuration caches to force reload with new structure
    await enhancedConfigService.clearCache();

    console.log('✅ Table mapping cache updated\n');
  }

  /**
   * Get default index strategy based on category
   */
  private getDefaultIndexStrategy(category: string): any {
    const strategies: Record<string, any> = {
      '医疗器械': {
        searchIndexes: ['productName', 'companyName', 'registrationNumber'],
        compositeIndexes: [
          { fields: ['category', 'status'], name: 'category_status_idx' }
        ]
      },
      '药品信息': {
        searchIndexes: ['drugName', 'manufacturer', 'approvalNumber'],
        compositeIndexes: [
          { fields: ['drugName', 'dosageForm'], name: 'drug_dosage_idx' }
        ]
      },
      'default': {
        searchIndexes: ['name', 'code'],
        compositeIndexes: []
      }
    };

    return strategies[category] || strategies.default;
  }

  /**
   * Print migration summary
   */
  private printMigrationSummary(): void {
    console.log('\n📋 Migration Summary:');
    console.log('═'.repeat(50));
    console.log(`Total Configurations: ${this.stats.totalConfigs}`);
    console.log(`Updated Configurations: ${this.stats.updatedConfigs}`);
    console.log(`Failed Configurations: ${this.stats.failedConfigs}`);
    console.log(`Added Fields: ${this.stats.addedFields.join(', ')}`);
    console.log(`Removed Fields: ${this.stats.removedFields.join(', ')}`);
    console.log('═'.repeat(50));

    if (this.options.dryRun) {
      console.log('\n🔍 This was a DRY RUN - no changes were made to the database');
      console.log('Run without --dry-run to apply changes');
    }
  }

  /**
   * Clean up database connection
   */
  async cleanup(): Promise<void> {
    if (this.connection) {
      await this.connection.end();
    }
  }
}

/**
 * Main execution function
 */
async function main() {
  const args = process.argv.slice(2);
  const options: MigrationOptions = {
    dryRun: args.includes('--dry-run'),
    verbose: args.includes('--verbose'),
    skipValidation: args.includes('--skip-validation'),
  };

  if (args.includes('--help')) {
    console.log(`
Enhanced Drizzle Configuration Migration

Usage: npx tsx scripts/migrate-to-enhanced-drizzle-config.ts [options]

Options:
  --dry-run           Preview changes without applying them
  --verbose           Show detailed output
  --skip-validation   Skip configuration validation
  --help              Show this help message

Examples:
  npx tsx scripts/migrate-to-enhanced-drizzle-config.ts --dry-run --verbose
  npx tsx scripts/migrate-to-enhanced-drizzle-config.ts
    `);
    process.exit(0);
  }

  let migration: EnhancedDrizzleMigration | null = null;

  try {
    migration = new EnhancedDrizzleMigration(options);
    await migration.migrate();
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  } finally {
    if (migration) {
      await migration.cleanup();
    }
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

export { EnhancedDrizzleMigration };
