#!/usr/bin/env tsx

import { config } from 'dotenv';

// 加载环境变量
config({ path: '.env.local' });

/**
 * 测试迁移后的 API 端点
 */
async function testApiEndpoints() {
  console.log('🧪 测试迁移后的 API 端点...');

  const baseUrl = 'http://localhost:3000';
  
  // 测试端点列表
  const testCases = [
    {
      name: '数据库配置查询',
      url: `${baseUrl}/api/meta/us_pmn`,
      method: 'GET',
      expectedStatus: 200,
    },
    {
      name: '统一搜索 API',
      url: `${baseUrl}/api/unified-database-search/us_pmn?q=device&limit=5`,
      method: 'GET',
      expectedStatus: 200,
    },
    {
      name: '数据统计 API',
      url: `${baseUrl}/api/stats/us_pmn`,
      method: 'GET',
      expectedStatus: 200,
    },
    {
      name: '高级搜索 API',
      url: `${baseUrl}/api/advanced-search/us_pmn?page=1&limit=5`,
      method: 'GET',
      expectedStatus: 200,
    }
  ];

  let passedTests = 0;
  let totalTests = testCases.length;

  for (const testCase of testCases) {
    try {
      console.log(`\n🔍 测试: ${testCase.name}`);
      console.log(`📡 请求: ${testCase.method} ${testCase.url}`);

      const response = await fetch(testCase.url, {
        method: testCase.method,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const status = response.status;
      const isSuccess = status === testCase.expectedStatus;

      if (isSuccess) {
        console.log(`✅ 状态码: ${status} (预期: ${testCase.expectedStatus})`);
        
        // 尝试解析响应数据
        try {
          const data = await response.json();
          if (data.success !== false) {
            console.log(`📊 响应数据: ${JSON.stringify(data).substring(0, 100)}...`);
            passedTests++;
          } else {
            console.log(`⚠️  API 返回错误: ${data.error}`);
          }
        } catch (parseError) {
          console.log(`⚠️  响应解析失败，但状态码正确`);
          passedTests++;
        }
      } else {
        console.log(`❌ 状态码: ${status} (预期: ${testCase.expectedStatus})`);
        
        // 尝试获取错误信息
        try {
          const errorData = await response.text();
          console.log(`🔍 错误详情: ${errorData.substring(0, 200)}...`);
        } catch (e) {
          console.log(`🔍 无法获取错误详情`);
        }
      }

    } catch (error) {
      console.log(`❌ 请求失败: ${error instanceof Error ? error.message : error}`);
    }

    // 添加延迟避免请求过快
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  console.log(`\n📊 测试结果总结:`);
  console.log(`✅ 通过: ${passedTests}/${totalTests}`);
  console.log(`❌ 失败: ${totalTests - passedTests}/${totalTests}`);
  console.log(`📈 成功率: ${Math.round((passedTests / totalTests) * 100)}%`);

  if (passedTests === totalTests) {
    console.log(`\n🎉 所有 API 测试通过！迁移成功！`);
    return true;
  } else {
    console.log(`\n⚠️  部分 API 测试失败，需要进一步检查`);
    return false;
  }
}

/**
 * 检查开发服务器是否运行
 */
async function checkDevServer() {
  console.log('🔍 检查开发服务器状态...');
  
  try {
    const response = await fetch('http://localhost:3000/api/health', {
      method: 'GET',
      timeout: 5000,
    });
    
    if (response.ok) {
      console.log('✅ 开发服务器正在运行');
      return true;
    } else {
      console.log('⚠️  开发服务器响应异常');
      return false;
    }
  } catch (error) {
    console.log('❌ 开发服务器未运行或无法访问');
    console.log('💡 请先运行: npm run dev');
    return false;
  }
}

// 主函数
async function main() {
  console.log('🚀 开始 API 端点测试...\n');

  // 检查服务器状态
  const serverRunning = await checkDevServer();
  
  if (!serverRunning) {
    console.log('\n🛑 测试终止：开发服务器未运行');
    console.log('请先启动开发服务器：npm run dev');
    process.exit(1);
  }

  // 运行 API 测试
  const allTestsPassed = await testApiEndpoints();

  if (allTestsPassed) {
    console.log('\n🎯 迁移验证完成！');
    console.log('✅ Drizzle ORM 迁移成功');
    console.log('✅ 所有功能正常工作');
    console.log('✅ API 兼容性完整保持');
    process.exit(0);
  } else {
    console.log('\n🔧 需要进一步调试和修复');
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  main().catch(error => {
    console.error('API 测试脚本执行失败:', error);
    process.exit(1);
  });
}
