#!/usr/bin/env tsx

/**
 * 测试筛选器联动修复效果
 * 验证搜索 "dental" 后，third party 筛选项的统计数字是否基于搜索结果
 */

async function testFilterLinkageFix() {
  console.log('🔍 测试筛选器联动修复效果...');
  
  const baseUrl = 'http://localhost:3000';
  
  try {
    // 1. 测试搜索 "dental" 的总数
    console.log('\n📊 1. 测试搜索 "dental" 的总数...');
    
    const searchResponse = await fetch(`${baseUrl}/api/unified-database-search/us_pmn?q=dental&page=1&limit=20`);
    const searchResult = await searchResponse.json();
    
    if (!searchResult.success) {
      console.error('❌ 搜索请求失败:', searchResult.error);
      return;
    }
    
    const totalDentalRecords = searchResult.pagination?.total_results || 0;
    console.log(`✅ 搜索 "dental" 的总记录数: ${totalDentalRecords}`);
    
    // 2. 测试动态计数API - 在搜索 "dental" 的基础上获取 thirdparty 的分布
    console.log('\n📊 2. 测试动态计数API - 基于 "dental" 搜索结果...');

    const filters = {
      allFields: 'dental'  // 包含文本搜索条件
    };

    const params = new URLSearchParams({
      field: 'thirdparty',
      filters: JSON.stringify(filters)
    });

    const dynamicCountsResponse = await fetch(`${baseUrl}/api/meta/us_pmn/dynamic-counts?${params}`);
    const dynamicCountsResult = await dynamicCountsResponse.json();

    if (!dynamicCountsResult.success) {
      console.error('❌ 动态计数请求失败:', dynamicCountsResult.error);
      return;
    }

    console.log('✅ 基于 "dental" 搜索的 thirdparty 分布:');
    let totalFromCounts = 0;
    dynamicCountsResult.data.forEach((item: any) => {
      const value = item.value || 'N/A';
      const count = item.count;
      totalFromCounts += count;
      console.log(`   ${value}: ${count} 条`);
    });
    
    console.log(`   总计: ${totalFromCounts} 条`);
    
    // 3. 验证数字是否匹配
    console.log('\n🔍 3. 验证数字匹配性...');
    
    if (totalFromCounts === totalDentalRecords) {
      console.log('✅ 修复成功！动态计数总数与搜索结果总数匹配');
      console.log(`   搜索结果总数: ${totalDentalRecords}`);
      console.log(`   动态计数总数: ${totalFromCounts}`);
    } else {
      console.log('❌ 数字不匹配，可能还有问题');
      console.log(`   搜索结果总数: ${totalDentalRecords}`);
      console.log(`   动态计数总数: ${totalFromCounts}`);
      console.log(`   差异: ${Math.abs(totalFromCounts - totalDentalRecords)}`);
    }
    
    // 4. 对比修复前的行为（全库统计）
    console.log('\n📊 4. 对比：全库 thirdparty 分布（修复前的行为）...');

    const globalParams = new URLSearchParams({
      field: 'thirdparty',
      filters: JSON.stringify({}) // 空筛选条件
    });

    const globalCountsResponse = await fetch(`${baseUrl}/api/meta/us_pmn/dynamic-counts?${globalParams}`);
    const globalCountsResult = await globalCountsResponse.json();

    if (globalCountsResult.success) {
      console.log('全库 thirdparty 分布:');
      let globalTotal = 0;
      globalCountsResult.data.forEach((item: any) => {
        const value = item.value || 'N/A';
        const count = item.count;
        globalTotal += count;
        console.log(`   ${value}: ${count} 条`);
      });
      console.log(`   全库总计: ${globalTotal} 条`);
      
      if (globalTotal > totalFromCounts) {
        console.log('✅ 修复验证：基于搜索的统计数字小于全库统计，符合预期');
      }
    }
    
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
  }
}

// 运行测试
testFilterLinkageFix().catch(console.error);
