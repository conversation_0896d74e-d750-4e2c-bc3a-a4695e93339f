#!/usr/bin/env tsx

import { readFileSync, writeFileSync, existsSync } from 'fs';

/**
 * 最终清理脚本
 * 修复剩余的导入问题和 ESLint 警告
 */
class FinalCleanup {
  
  /**
   * 修复 API 路由中的导入问题
   */
  fixApiRoutes(): void {
    console.log('🔧 修复 API 路由导入问题...');

    const apiFiles = [
      'src/app/api/advanced-search/[database]/route.ts',
      'src/app/api/data/[database]/[id]/route.ts',
      'src/app/api/data/[database]/route.ts',
      'src/app/api/export/[database]/route.ts',
      'src/app/api/meta/[database]/dynamic-counts/route.ts',
      'src/app/api/meta/[database]/route.ts',
      'src/app/api/stats/[database]/configurable/route.ts',
      'src/app/api/stats/[database]/route.ts',
      'src/app/data/detail/[database]/[id]/page.tsx'
    ];

    const replacements = [
      {
        from: "import { getDynamicModel, isPrismaModel } from '@/lib/dynamicTableMapping';",
        to: "import { getDynamicTable, isDrizzleTable } from '@/lib/drizzleTableMapping';"
      },
      {
        from: "import { getDynamicModel } from '@/lib/dynamicTableMapping';",
        to: "import { getDynamicTable } from '@/lib/drizzleTableMapping';"
      },
      {
        from: "import { isPrismaModel } from '@/lib/dynamicTableMapping';",
        to: "import { isDrizzleTable } from '@/lib/drizzleTableMapping';"
      },
      {
        from: "getDynamicModel(",
        to: "getDynamicTable("
      },
      {
        from: "isPrismaModel(",
        to: "isDrizzleTable("
      },
      {
        from: "isDynamicPrismaModel(",
        to: "isDrizzleTable("
      },
      {
        from: "import { buildMedicalDeviceWhere } from '@/lib/server/buildMedicalDeviceWhere';",
        to: "import { buildDrizzleWhere } from '@/lib/server/buildDrizzleWhere';"
      },
      {
        from: "buildMedicalDeviceWhere(",
        to: "buildDrizzleWhere("
      }
    ];

    let totalFixed = 0;

    for (const file of apiFiles) {
      if (existsSync(file)) {
        let content = readFileSync(file, 'utf-8');
        let modified = false;

        for (const replacement of replacements) {
          if (content.includes(replacement.from)) {
            content = content.replace(new RegExp(replacement.from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), replacement.to);
            modified = true;
          }
        }

        if (modified) {
          writeFileSync(file, content);
          console.log(`✅ 修复: ${file}`);
          totalFixed++;
        }
      }
    }

    console.log(`📊 修复了 ${totalFixed} 个 API 文件`);
  }

  /**
   * 修复 ESLint 警告
   */
  fixEslintWarnings(): void {
    console.log('\n🧹 修复 ESLint 警告...');

    const fixes = [
      {
        file: 'src/lib/server/buildDrizzleWhere.ts',
        replacements: [
          {
            from: "import { and, or, eq, ne, like, ilike, gte, lte, between, isNull, inArray, SQL } from 'drizzle-orm';",
            to: "import { and, or, eq, ilike, gte, lte, between, isNull, inArray, SQL } from 'drizzle-orm';"
          }
        ]
      },
      {
        file: 'src/lib/server/buildMedicalDeviceWhere.ts',
        replacements: [
          {
            from: "import { and, or, eq, ne, like, ilike, gte, lte, between, isNull, inArray, SQL } from 'drizzle-orm';",
            to: "import { and, or, eq, ilike, gte, lte, between, isNull, inArray, SQL } from 'drizzle-orm';"
          }
        ]
      },
      {
        file: 'src/lib/services/drizzleFetchService.ts',
        replacements: [
          {
            from: "import { buildDrizzleWhere, buildDrizzleOrderBy, buildDrizzleSelect } from '@/lib/server/buildDrizzleWhere';",
            to: "import { buildDrizzleWhere, buildDrizzleOrderBy } from '@/lib/server/buildDrizzleWhere';"
          },
          {
            from: "import { eq, inArray, count, asc, desc } from 'drizzle-orm';",
            to: "import { inArray, count, asc, desc } from 'drizzle-orm';"
          },
          {
            from: "import type { DatabaseConfig } from '@/lib/configCache';",
            to: "import type { DatabaseConfig } from '@/lib/drizzleConfigCache';"
          }
        ]
      }
    ];

    let totalFixed = 0;

    for (const fix of fixes) {
      if (existsSync(fix.file)) {
        let content = readFileSync(fix.file, 'utf-8');
        let modified = false;

        for (const replacement of fix.replacements) {
          if (content.includes(replacement.from)) {
            content = content.replace(replacement.from, replacement.to);
            modified = true;
          }
        }

        if (modified) {
          writeFileSync(fix.file, content);
          console.log(`✅ 修复: ${fix.file}`);
          totalFixed++;
        }
      }
    }

    console.log(`📊 修复了 ${totalFixed} 个 ESLint 警告`);
  }

  /**
   * 移除未使用的导入
   */
  removeUnusedImports(): void {
    console.log('\n🗑️  移除未使用的导入...');

    const files = [
      'src/app/page.tsx',
      'src/components/Navigation.tsx',
      'src/app/debug/loading/page.tsx'
    ];

    const unusedImports = [
      'loadDatabaseConfigs',
      'addConfigListener', 
      'getCurrentConfigs',
      'getOptimizedDatabaseConfigs',
      'getOptimizedNavigationConfigs',
      'useRouter'
    ];

    let totalFixed = 0;

    for (const file of files) {
      if (existsSync(file)) {
        let content = readFileSync(file, 'utf-8');
        let modified = false;

        // 移除未使用的导入
        for (const unusedImport of unusedImports) {
          const patterns = [
            new RegExp(`import\\s*{[^}]*\\b${unusedImport}\\b[^}]*}\\s*from[^;]+;`, 'g'),
            new RegExp(`\\b${unusedImport}\\b,?\\s*`, 'g')
          ];

          for (const pattern of patterns) {
            if (pattern.test(content)) {
              content = content.replace(pattern, (match) => {
                if (match.includes('import')) {
                  // 如果整个导入语句只包含这个未使用的导入，删除整行
                  const cleanMatch = match.replace(new RegExp(`\\b${unusedImport}\\b,?\\s*`, 'g'), '');
                  if (cleanMatch.match(/import\s*{\s*}\s*from/)) {
                    return '';
                  }
                  return cleanMatch;
                } else {
                  return match.replace(new RegExp(`\\b${unusedImport}\\b,?\\s*`, 'g'), '');
                }
              });
              modified = true;
            }
          }
        }

        if (modified) {
          writeFileSync(file, content);
          console.log(`✅ 清理: ${file}`);
          totalFixed++;
        }
      }
    }

    console.log(`📊 清理了 ${totalFixed} 个文件`);
  }

  /**
   * 执行所有清理任务
   */
  runAllCleanup(): void {
    console.log('🧹 开始最终清理...\n');

    this.fixApiRoutes();
    this.fixEslintWarnings();
    this.removeUnusedImports();

    console.log('\n✅ 最终清理完成！');
    console.log('🎯 现在可以运行以下命令测试：');
    console.log('  npm run build  # 验证构建');
    console.log('  npm run dev    # 启动开发服务器');
  }
}

// 运行清理
if (require.main === module) {
  const cleanup = new FinalCleanup();
  cleanup.runAllCleanup();
}
