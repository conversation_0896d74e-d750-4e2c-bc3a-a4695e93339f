#!/usr/bin/env tsx

import { db } from '../src/lib/drizzle';
import { databaseConfigs } from '../src/db/schema';

/**
 * 测试 Drizzle 连接和基本查询
 */
async function testDrizzleConnection() {
  console.log('🔍 测试 Drizzle 数据库连接...');

  try {
    // 测试基本连接
    console.log('📡 测试数据库连接...');
    
    // 尝试查询现有的数据库配置
    const configs = await db.select().from(databaseConfigs).limit(5);
    
    console.log('✅ 数据库连接成功！');
    console.log(`📊 找到 ${configs.length} 个数据库配置:`);
    
    configs.forEach((config, index) => {
      console.log(`  ${index + 1}. ${config.name} (${config.code})`);
    });

    // 测试计数查询
    console.log('\n🔢 测试计数查询...');
    const countResult = await db.$count(databaseConfigs);
    console.log(`✅ 总配置数量: ${countResult}`);

    console.log('\n🎉 所有测试通过！Drizzle 连接正常工作。');

  } catch (error) {
    console.error('❌ Drizzle 连接测试失败:', error);
    
    if (error instanceof Error) {
      console.error('错误详情:', error.message);
      console.error('错误堆栈:', error.stack);
    }
    
    console.log('\n🔧 可能的解决方案:');
    console.log('1. 检查 DATABASE_URL 环境变量');
    console.log('2. 确保数据库服务正在运行');
    console.log('3. 验证数据库表结构是否存在');
    
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  testDrizzleConnection().catch(error => {
    console.error('测试脚本执行失败:', error);
    process.exit(1);
  });
}
