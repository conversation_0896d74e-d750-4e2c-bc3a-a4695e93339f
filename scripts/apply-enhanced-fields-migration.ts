#!/usr/bin/env tsx

/**
 * 应用增强字段迁移脚本
 * 直接通过代码执行 SQL 迁移，添加新的增强字段
 */

import { config } from 'dotenv';
import postgres from 'postgres';
import { drizzle } from 'drizzle-orm/postgres-js';
import { sql } from 'drizzle-orm';

// 加载环境变量
config({ path: '.env.local' });

async function applyMigration() {
  console.log('🚀 开始应用增强字段迁移...\n');

  const connectionString = process.env.DATABASE_URL;
  if (!connectionString) {
    console.error('❌ DATABASE_URL 环境变量未设置');
    return false;
  }

  let connection: postgres.Sql;
  let db: ReturnType<typeof drizzle>;

  try {
    // 创建连接
    connection = postgres(connectionString, {
      max: 5,
      idle_timeout: 20,
      connect_timeout: 10,
    });

    db = drizzle(connection);

    console.log('✅ 数据库连接成功');

    // 检查表是否存在
    const tableExists = await db.execute(sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'DatabaseConfig'
      ) as exists
    `);

    if (!(tableExists[0] as any).exists) {
      console.error('❌ DatabaseConfig 表不存在');
      return false;
    }

    console.log('✅ DatabaseConfig 表存在');

    // 开始迁移
    console.log('\n🔧 开始添加增强字段...');

    // 1. 添加 schemaName 字段
    try {
      await db.execute(sql`ALTER TABLE "DatabaseConfig" ADD COLUMN "schemaName" varchar(100) DEFAULT 'public'`);
      console.log('✅ 添加 schemaName 字段');
    } catch (error: any) {
      if (error.message.includes('already exists')) {
        console.log('⏭️ schemaName 字段已存在');
      } else {
        throw error;
      }
    }

    // 2. 添加 connectionPool 字段
    try {
      await db.execute(sql`ALTER TABLE "DatabaseConfig" ADD COLUMN "connectionPool" varchar(50) DEFAULT 'default'`);
      console.log('✅ 添加 connectionPool 字段');
    } catch (error: any) {
      if (error.message.includes('already exists')) {
        console.log('⏭️ connectionPool 字段已存在');
      } else {
        throw error;
      }
    }

    // 3. 添加 queryTimeout 字段
    try {
      await db.execute(sql`ALTER TABLE "DatabaseConfig" ADD COLUMN "queryTimeout" integer DEFAULT 30000`);
      console.log('✅ 添加 queryTimeout 字段');
    } catch (error: any) {
      if (error.message.includes('already exists')) {
        console.log('⏭️ queryTimeout 字段已存在');
      } else {
        throw error;
      }
    }

    // 4. 添加 cacheStrategy 字段
    try {
      await db.execute(sql`ALTER TABLE "DatabaseConfig" ADD COLUMN "cacheStrategy" varchar(50) DEFAULT 'redis'`);
      console.log('✅ 添加 cacheStrategy 字段');
    } catch (error: any) {
      if (error.message.includes('already exists')) {
        console.log('⏭️ cacheStrategy 字段已存在');
      } else {
        throw error;
      }
    }

    // 5. 添加 cacheTTL 字段
    try {
      await db.execute(sql`ALTER TABLE "DatabaseConfig" ADD COLUMN "cacheTTL" integer DEFAULT 300`);
      console.log('✅ 添加 cacheTTL 字段');
    } catch (error: any) {
      if (error.message.includes('already exists')) {
        console.log('⏭️ cacheTTL 字段已存在');
      } else {
        throw error;
      }
    }

    // 6. 添加 exportFormats 字段
    try {
      await db.execute(sql`ALTER TABLE "DatabaseConfig" ADD COLUMN "exportFormats" json DEFAULT '["csv", "xlsx"]'`);
      console.log('✅ 添加 exportFormats 字段');
    } catch (error: any) {
      if (error.message.includes('already exists')) {
        console.log('⏭️ exportFormats 字段已存在');
      } else {
        throw error;
      }
    }

    // 7. 添加 indexStrategy 字段
    try {
      await db.execute(sql`ALTER TABLE "DatabaseConfig" ADD COLUMN "indexStrategy" json`);
      console.log('✅ 添加 indexStrategy 字段');
    } catch (error: any) {
      if (error.message.includes('already exists')) {
        console.log('⏭️ indexStrategy 字段已存在');
      } else {
        throw error;
      }
    }

    console.log('\n🔄 更新现有记录的默认值...');

    // 更新现有记录的默认值
    await db.execute(sql`
      UPDATE "DatabaseConfig" SET 
        "schemaName" = 'public',
        "connectionPool" = 'default',
        "queryTimeout" = 30000,
        "cacheStrategy" = 'redis',
        "cacheTTL" = 300,
        "exportFormats" = '["csv", "xlsx"]'
      WHERE "schemaName" IS NULL
    `);

    console.log('✅ 现有记录已更新默认值');

    console.log('\n📊 创建索引以提升性能...');

    // 创建索引
    try {
      await db.execute(sql`CREATE INDEX IF NOT EXISTS "idx_database_config_schema_name" ON "DatabaseConfig"("schemaName")`);
      console.log('✅ 创建 schemaName 索引');
    } catch (error) {
      console.log('⏭️ schemaName 索引已存在');
    }

    try {
      await db.execute(sql`CREATE INDEX IF NOT EXISTS "idx_database_config_connection_pool" ON "DatabaseConfig"("connectionPool")`);
      console.log('✅ 创建 connectionPool 索引');
    } catch (error) {
      console.log('⏭️ connectionPool 索引已存在');
    }

    try {
      await db.execute(sql`CREATE INDEX IF NOT EXISTS "idx_database_config_cache_strategy" ON "DatabaseConfig"("cacheStrategy")`);
      console.log('✅ 创建 cacheStrategy 索引');
    } catch (error) {
      console.log('⏭️ cacheStrategy 索引已存在');
    }

    console.log('\n🎉 迁移完成！');

    // 验证迁移结果
    console.log('\n🔍 验证迁移结果...');
    const columns = await db.execute(sql`
      SELECT column_name
      FROM information_schema.columns 
      WHERE table_name = 'DatabaseConfig' 
      AND table_schema = 'public'
      ORDER BY ordinal_position
    `);

    const columnNames = (columns as any[]).map(col => col.column_name);
    const expectedNewFields = [
      'schemaName',
      'connectionPool', 
      'queryTimeout',
      'cacheStrategy',
      'cacheTTL',
      'exportFormats',
      'indexStrategy'
    ];

    const existing = expectedNewFields.filter(field => columnNames.includes(field));
    const missing = expectedNewFields.filter(field => !columnNames.includes(field));

    console.log(`✅ 成功添加字段 (${existing.length}/${expectedNewFields.length}):`, existing.join(', '));
    
    if (missing.length > 0) {
      console.log(`❌ 仍然缺失的字段:`, missing.join(', '));
      return false;
    }

    // 显示示例数据
    console.log('\n📋 示例配置数据:');
    const sampleConfigs = await db.execute(sql`
      SELECT 
        code, 
        name, 
        "tableName",
        "schemaName",
        "connectionPool",
        "queryTimeout",
        "cacheStrategy",
        "cacheTTL"
      FROM "DatabaseConfig" 
      LIMIT 2
    `);

    (sampleConfigs as any[]).forEach((config, index) => {
      console.log(`\n  ${index + 1}. ${config.name} (${config.code})`);
      console.log(`     表名: ${config.tableName || 'N/A'}`);
      console.log(`     Schema: ${config.schemaName || 'N/A'}`);
      console.log(`     连接池: ${config.connectionPool || 'N/A'}`);
      console.log(`     查询超时: ${config.queryTimeout || 'N/A'}ms`);
      console.log(`     缓存策略: ${config.cacheStrategy || 'N/A'}`);
      console.log(`     缓存TTL: ${config.cacheTTL || 'N/A'}s`);
    });

    console.log('\n🎉 增强字段迁移成功完成！');
    console.log('✅ 数据库已准备就绪，可以使用增强的 Drizzle 配置系统');
    console.log('\n下一步可以运行:');
    console.log('npx tsx scripts/migrate-to-enhanced-drizzle-config.ts --dry-run');

    return true;

  } catch (error) {
    console.error('❌ 迁移失败:', error);
    return false;
  } finally {
    if (connection!) {
      await connection.end();
    }
  }
}

// 主执行
applyMigration()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('迁移失败:', error);
    process.exit(1);
  });
