#!/usr/bin/env tsx

/**
 * 测试新的Drizzle搜索功能
 */

import { DrizzleSearchService } from '../src/lib/services/drizzleSearchService';
import { initializeDatabaseServers } from '../src/lib/database-servers';

async function testDrizzleSearch() {
  console.log('🚀 测试Drizzle搜索功能...\n');

  try {
    // 初始化数据库服务器配置
    await initializeDatabaseServers();
    console.log('✅ 数据库服务器配置初始化完成\n');

    // 测试1: 基本搜索
    console.log('📋 测试1: 基本搜索 (us_pmn)');
    const basicSearch = await DrizzleSearchService.search({
      database: 'us_pmn',
      page: 1,
      limit: 5
    });

    console.log(`   结果: ${basicSearch.success ? '成功' : '失败'}`);
    console.log(`   数据条数: ${basicSearch.data.length}`);
    console.log(`   总记录数: ${basicSearch.pagination.total}`);
    console.log(`   搜索耗时: ${basicSearch.searchInfo.searchTime.toFixed(2)}ms\n`);

    // 测试2: 条件搜索
    console.log('📋 测试2: 条件搜索 (device name = dental)');
    const conditionSearch = await DrizzleSearchService.search({
      database: 'us_pmn',
      conditions: [
        {
          id: 'test_1',
          field: 'devicename',
          value: 'dental'
        }
      ],
      page: 1,
      limit: 5
    });

    console.log(`   结果: ${conditionSearch.success ? '成功' : '失败'}`);
    console.log(`   数据条数: ${conditionSearch.data.length}`);
    console.log(`   总记录数: ${conditionSearch.pagination.total}`);
    console.log(`   搜索耗时: ${conditionSearch.searchInfo.searchTime.toFixed(2)}ms`);
    
    if (conditionSearch.data.length > 0) {
      console.log(`   示例记录: ${JSON.stringify(conditionSearch.data[0], null, 2).substring(0, 200)}...\n`);
    }

    // 测试3: 全局关键词搜索
    console.log('📋 测试3: 全局关键词搜索 (dental)');
    const keywordSearch = await DrizzleSearchService.search({
      database: 'us_pmn',
      globalKeyword: 'dental',
      page: 1,
      limit: 5
    });

    console.log(`   结果: ${keywordSearch.success ? '成功' : '失败'}`);
    console.log(`   数据条数: ${keywordSearch.data.length}`);
    console.log(`   总记录数: ${keywordSearch.pagination.total}`);
    console.log(`   搜索耗时: ${keywordSearch.searchInfo.searchTime.toFixed(2)}ms`);
    if (keywordSearch.searchInfo.esSearchTime) {
      console.log(`   ES搜索耗时: ${keywordSearch.searchInfo.esSearchTime.toFixed(2)}ms`);
    }
    console.log();

    // 测试4: 字段统计
    console.log('📋 测试4: 字段统计 (devicename)');
    const fieldStats = await DrizzleSearchService.getFieldStatistics(
      'us_pmn',
      'devicename',
      [],
      10
    );

    console.log(`   字段: ${fieldStats.field}`);
    console.log(`   统计项数: ${fieldStats.items.length}`);
    console.log(`   总记录数: ${fieldStats.total}`);
    console.log(`   前5项:`);
    fieldStats.items.slice(0, 5).forEach((item, index) => {
      console.log(`     ${index + 1}. ${item.value} (${item.count})`);
    });
    console.log();

    // 测试5: 复合条件搜索
    console.log('📋 测试5: 复合条件搜索');
    const complexSearch = await DrizzleSearchService.search({
      database: 'us_pmn',
      conditions: [
        {
          id: 'test_1',
          field: 'devicename',
          value: 'dental'
        },
        {
          id: 'test_2',
          field: 'decision',
          value: 'SE'
        }
      ],
      page: 1,
      limit: 3
    });

    console.log(`   结果: ${complexSearch.success ? '成功' : '失败'}`);
    console.log(`   数据条数: ${complexSearch.data.length}`);
    console.log(`   总记录数: ${complexSearch.pagination.total}`);
    console.log(`   搜索耗时: ${complexSearch.searchInfo.searchTime.toFixed(2)}ms\n`);

    // 测试6: 排序功能
    console.log('📋 测试6: 排序功能 (按decisiondate降序)');
    const sortedSearch = await DrizzleSearchService.search({
      database: 'us_pmn',
      sortBy: 'decisiondate',
      sortOrder: 'desc',
      page: 1,
      limit: 3
    });

    console.log(`   结果: ${sortedSearch.success ? '成功' : '失败'}`);
    console.log(`   数据条数: ${sortedSearch.data.length}`);
    console.log(`   搜索耗时: ${sortedSearch.searchInfo.searchTime.toFixed(2)}ms`);
    
    if (sortedSearch.data.length > 0) {
      console.log(`   最新记录日期: ${sortedSearch.data[0].decisiondate || 'N/A'}\n`);
    }

    console.log('🎉 所有测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  testDrizzleSearch().catch(console.error);
}
