#!/usr/bin/env tsx

import { execSync } from 'child_process';
import { readFileSync, writeFileSync, existsSync, copyFileSync } from 'fs';
import { join } from 'path';

/**
 * Prisma 到 Drizzle 迁移脚本
 * 
 * 功能：
 * 1. 备份现有的 Prisma 相关文件
 * 2. 更新导入语句
 * 3. 替换服务文件
 * 4. 运行 Drizzle 迁移
 * 5. 验证迁移结果
 */

interface MigrationOptions {
  dryRun?: boolean;
  backup?: boolean;
  verbose?: boolean;
}

class DrizzleMigrationScript {
  private options: MigrationOptions;
  private backupDir = './backup-prisma';

  constructor(options: MigrationOptions = {}) {
    this.options = {
      dryRun: false,
      backup: true,
      verbose: true,
      ...options
    };
  }

  /**
   * 执行完整迁移
   */
  async migrate(): Promise<void> {
    console.log('🚀 开始 Prisma 到 Drizzle 迁移...\n');

    try {
      // 步骤1: 备份现有文件
      if (this.options.backup) {
        await this.backupFiles();
      }

      // 步骤2: 更新 package.json 脚本
      await this.updatePackageScripts();

      // 步骤3: 替换核心服务文件
      await this.replaceServiceFiles();

      // 步骤4: 更新导入语句
      await this.updateImports();

      // 步骤5: 运行 Drizzle 迁移
      if (!this.options.dryRun) {
        await this.runDrizzleMigration();
      }

      // 步骤6: 验证迁移
      await this.validateMigration();

      console.log('\n✅ Drizzle 迁移完成！');
      console.log('\n📋 后续步骤：');
      console.log('1. 运行 `npm run drizzle:push` 应用数据库更改');
      console.log('2. 测试关键 API 端点');
      console.log('3. 运行完整的功能测试');
      console.log('4. 如果一切正常，可以删除备份文件');

    } catch (error) {
      console.error('\n❌ 迁移失败:', error);
      console.log('\n🔄 回滚建议：');
      console.log('1. 从备份恢复文件');
      console.log('2. 运行 `npm install` 重新安装依赖');
      console.log('3. 运行 `npm run prisma:generate` 重新生成 Prisma 客户端');
      throw error;
    }
  }

  /**
   * 备份现有文件
   */
  private async backupFiles(): Promise<void> {
    console.log('📦 备份现有文件...');

    const filesToBackup = [
      'src/lib/prisma.ts',
      'src/lib/configCache.ts',
      'src/lib/dynamicTableMapping.ts',
      'src/lib/services/prismaFetchService.ts',
      'src/lib/services/unifiedSearchService.ts',
      'src/lib/server/buildMedicalDeviceWhere.ts',
      'src/app/api/unified-database-search/[database]/route.ts',
    ];

    // 创建备份目录
    if (!this.options.dryRun) {
      execSync(`mkdir -p ${this.backupDir}`, { stdio: 'inherit' });
    }

    for (const file of filesToBackup) {
      if (existsSync(file)) {
        const backupPath = join(this.backupDir, file.replace(/\//g, '_'));
        if (!this.options.dryRun) {
          copyFileSync(file, backupPath);
        }
        console.log(`  ✓ 备份: ${file} -> ${backupPath}`);
      }
    }
  }

  /**
   * 更新 package.json 脚本
   */
  private async updatePackageScripts(): Promise<void> {
    console.log('\n📝 更新 package.json 脚本...');

    const packagePath = 'package.json';
    const packageContent = readFileSync(packagePath, 'utf-8');
    const packageJson = JSON.parse(packageContent);

    // 添加新的 Drizzle 脚本
    packageJson.scripts = {
      ...packageJson.scripts,
      'db:generate': 'drizzle-kit generate',
      'db:migrate': 'drizzle-kit migrate',
      'db:studio': 'drizzle-kit studio',
      'db:push': 'drizzle-kit push',
      'db:seed-drizzle': 'tsx src/db/seed-drizzle.ts',
    };

    if (!this.options.dryRun) {
      writeFileSync(packagePath, JSON.stringify(packageJson, null, 2));
    }

    console.log('  ✓ 添加 Drizzle 脚本到 package.json');
  }

  /**
   * 替换核心服务文件
   */
  private async replaceServiceFiles(): Promise<void> {
    console.log('\n🔄 替换核心服务文件...');

    const replacements = [
      {
        from: 'src/lib/prisma.ts',
        to: 'src/lib/drizzle.ts',
        description: '数据库连接'
      },
      {
        from: 'src/lib/configCache.ts',
        to: 'src/lib/drizzleConfigCache.ts',
        description: '配置缓存服务'
      },
      {
        from: 'src/lib/dynamicTableMapping.ts',
        to: 'src/lib/drizzleTableMapping.ts',
        description: '动态表映射'
      },
      {
        from: 'src/lib/services/prismaFetchService.ts',
        to: 'src/lib/services/drizzleFetchService.ts',
        description: '数据回捞服务'
      },
      {
        from: 'src/lib/services/unifiedSearchService.ts',
        to: 'src/lib/services/drizzleUnifiedSearchService.ts',
        description: '统一搜索服务'
      },
      {
        from: 'src/lib/server/buildMedicalDeviceWhere.ts',
        to: 'src/lib/server/buildDrizzleWhere.ts',
        description: '查询构建器'
      }
    ];

    for (const replacement of replacements) {
      if (existsSync(replacement.to)) {
        if (!this.options.dryRun) {
          copyFileSync(replacement.to, replacement.from);
        }
        console.log(`  ✓ 替换: ${replacement.description}`);
      } else {
        console.log(`  ⚠️  文件不存在: ${replacement.to}`);
      }
    }
  }

  /**
   * 更新导入语句
   */
  private async updateImports(): Promise<void> {
    console.log('\n🔧 更新导入语句...');

    const filesToUpdate = [
      'src/app/api/unified-database-search/[database]/route.ts',
      'src/app/api/data/[database]/[id]/route.ts',
      'src/app/api/stats/[database]/route.ts',
      'src/app/api/advanced-search/[database]/route.ts',
    ];

    const importReplacements = [
      {
        from: "import { db } from '@/lib/prisma'",
        to: "import { db } from '@/lib/drizzle'"
      },
      {
        from: "import { getDynamicModel, isPrismaModel } from '@/lib/dynamicTableMapping'",
        to: "import { getDynamicTable, isDrizzleTable } from '@/lib/drizzleTableMapping'"
      },
      {
        from: "import { getDatabaseConfig } from '@/lib/configCache'",
        to: "import { getDatabaseConfig } from '@/lib/drizzleConfigCache'"
      },
      {
        from: "import { UnifiedSearchService } from '@/lib/services/unifiedSearchService'",
        to: "import { DrizzleUnifiedSearchService } from '@/lib/services/drizzleUnifiedSearchService'"
      },
      {
        from: "import { PrismaFetchService } from '@/lib/services/prismaFetchService'",
        to: "import { DrizzleFetchService } from '@/lib/services/drizzleFetchService'"
      }
    ];

    for (const file of filesToUpdate) {
      if (existsSync(file)) {
        let content = readFileSync(file, 'utf-8');
        let updated = false;

        for (const replacement of importReplacements) {
          if (content.includes(replacement.from)) {
            content = content.replace(replacement.from, replacement.to);
            updated = true;
          }
        }

        if (updated && !this.options.dryRun) {
          writeFileSync(file, content);
        }

        console.log(`  ${updated ? '✓' : '○'} ${file}`);
      }
    }
  }

  /**
   * 运行 Drizzle 迁移
   */
  private async runDrizzleMigration(): Promise<void> {
    console.log('\n🗄️ 运行 Drizzle 迁移...');

    try {
      // 生成迁移文件
      console.log('  📝 生成迁移文件...');
      execSync('npm run drizzle:generate', { stdio: 'inherit' });

      // 应用迁移（可选，用户可以手动执行）
      console.log('  ⚠️  请手动运行 `npm run drizzle:push` 应用数据库更改');

    } catch (error) {
      console.error('  ❌ Drizzle 迁移失败:', error);
      throw error;
    }
  }

  /**
   * 验证迁移结果
   */
  private async validateMigration(): Promise<void> {
    console.log('\n🔍 验证迁移结果...');

    const checks = [
      {
        name: 'Drizzle 配置文件',
        path: 'drizzle.config.ts',
        required: true
      },
      {
        name: 'Drizzle Schema',
        path: 'src/db/schema.ts',
        required: true
      },
      {
        name: 'Drizzle 连接',
        path: 'src/lib/drizzle.ts',
        required: true
      },
      {
        name: '迁移文件目录',
        path: 'drizzle',
        required: true
      }
    ];

    let allPassed = true;

    for (const check of checks) {
      const exists = existsSync(check.path);
      const status = exists ? '✓' : (check.required ? '❌' : '⚠️');
      console.log(`  ${status} ${check.name}: ${check.path}`);

      if (check.required && !exists) {
        allPassed = false;
      }
    }

    if (!allPassed) {
      throw new Error('迁移验证失败：缺少必需文件');
    }

    console.log('  ✅ 所有检查通过');
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  const options: MigrationOptions = {
    dryRun: args.includes('--dry-run'),
    backup: !args.includes('--no-backup'),
    verbose: !args.includes('--quiet')
  };

  if (options.dryRun) {
    console.log('🔍 运行模式：预览（不会实际修改文件）\n');
  }

  const migrator = new DrizzleMigrationScript(options);
  await migrator.migrate();
}

// 运行脚本
if (require.main === module) {
  main().catch(error => {
    console.error('迁移脚本执行失败:', error);
    process.exit(1);
  });
}

export { DrizzleMigrationScript };
