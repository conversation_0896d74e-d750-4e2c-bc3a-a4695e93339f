#!/usr/bin/env tsx

import { config } from 'dotenv';
import postgres from 'postgres';

// 加载环境变量
config({ path: '.env.local' });

/**
 * 简单的 Drizzle 连接测试
 * 不依赖复杂的 schema，直接测试原生 SQL
 */
async function testSimpleConnection() {
  console.log('🔍 测试简单数据库连接...');

  const connectionString = process.env.DATABASE_URL;
  if (!connectionString) {
    console.error('❌ DATABASE_URL 环境变量未设置');
    process.exit(1);
  }

  console.log('📡 连接字符串:', connectionString.replace(/:[^:@]*@/, ':***@'));

  try {
    // 创建简单的 postgres 连接
    const sql = postgres(connectionString, {
      max: 1,
      idle_timeout: 5,
      connect_timeout: 5,
    });

    console.log('🔌 尝试连接数据库...');

    // 测试基本查询
    const result = await sql`SELECT version()`;
    console.log('✅ 数据库连接成功！');
    console.log('📊 PostgreSQL 版本:', result[0].version);

    // 检查现有表
    console.log('\n📋 检查现有表结构...');
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `;

    console.log(`✅ 找到 ${tables.length} 个表:`);
    tables.forEach((table, index) => {
      console.log(`  ${index + 1}. ${table.table_name}`);
    });

    // 检查 DatabaseConfig 表
    console.log('\n🔍 检查 DatabaseConfig 表...');
    try {
      const configs = await sql`
        SELECT code, name, category 
        FROM "DatabaseConfig" 
        LIMIT 5
      `;
      console.log(`✅ DatabaseConfig 表存在，包含 ${configs.length} 条记录:`);
      configs.forEach((config, index) => {
        console.log(`  ${index + 1}. ${config.name} (${config.code})`);
      });
    } catch (error) {
      console.log('⚠️  DatabaseConfig 表可能不存在或结构不同');
    }

    // 关闭连接
    await sql.end();
    console.log('\n🎉 所有测试完成！数据库连接正常。');

  } catch (error) {
    console.error('❌ 数据库连接失败:', error);
    
    if (error instanceof Error) {
      console.error('错误详情:', error.message);
    }
    
    console.log('\n🔧 故障排除建议:');
    console.log('1. 检查 PostgreSQL 服务是否运行');
    console.log('2. 验证连接字符串中的用户名和密码');
    console.log('3. 确认数据库名称是否正确');
    console.log('4. 检查防火墙设置');
    
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  testSimpleConnection().catch(error => {
    console.error('测试脚本执行失败:', error);
    process.exit(1);
  });
}
