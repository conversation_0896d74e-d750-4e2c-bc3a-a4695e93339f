#!/usr/bin/env tsx

import { readFileSync, writeFileSync, existsSync } from 'fs';

/**
 * 修复客户端/服务器端导入问题
 * 确保 postgres 包只在服务器端被导入
 */
class ImportFixer {
  
  /**
   * 修复文件中的导入语句
   */
  fixImportsInFile(filePath: string, replacements: Array<{ from: string; to: string }>): boolean {
    if (!existsSync(filePath)) {
      console.log(`⚠️  文件不存在: ${filePath}`);
      return false;
    }

    let content = readFileSync(filePath, 'utf-8');
    let modified = false;

    for (const replacement of replacements) {
      if (content.includes(replacement.from)) {
        content = content.replace(new RegExp(replacement.from, 'g'), replacement.to);
        modified = true;
        console.log(`✅ ${filePath}: ${replacement.from} -> ${replacement.to}`);
      }
    }

    if (modified) {
      writeFileSync(filePath, content);
      return true;
    }

    return false;
  }

  /**
   * 执行所有修复
   */
  fixAllImports(): void {
    console.log('🔧 修复客户端/服务器端导入问题...\n');

    // 需要修复的文件和替换规则
    const fixes = [
      {
        file: 'src/lib/drizzleTableMapping.ts',
        replacements: [
          { from: "import { db } from './drizzle';", to: "import { db } from './db-server';" }
        ]
      },
      {
        file: 'src/lib/drizzleConfigCache.ts',
        replacements: [
          { from: "import { db } from './drizzle';", to: "import { db } from './db-server';" }
        ]
      },
      {
        file: 'src/lib/services/drizzleFetchService.ts',
        replacements: [
          { from: "import { db } from '@/lib/drizzle';", to: "import { db } from '@/lib/db-server';" }
        ]
      },
      {
        file: 'src/lib/services/drizzleUnifiedSearchService.ts',
        replacements: [
          { from: "import { db } from '@/lib/drizzle';", to: "import { db } from '@/lib/db-server';" }
        ]
      }
    ];

    let totalFixed = 0;

    for (const fix of fixes) {
      console.log(`📝 处理文件: ${fix.file}`);
      const wasFixed = this.fixImportsInFile(fix.file, fix.replacements);
      if (wasFixed) {
        totalFixed++;
      } else {
        console.log(`  ○ 无需修改`);
      }
    }

    console.log(`\n📊 修复完成: ${totalFixed} 个文件被修改`);

    // 验证修复结果
    this.verifyFixes();
  }

  /**
   * 验证修复结果
   */
  verifyFixes(): void {
    console.log('\n🔍 验证修复结果...');

    const serverOnlyFiles = [
      'src/lib/db-server.ts',
      'src/lib/drizzleTableMapping.ts',
      'src/lib/drizzleConfigCache.ts',
      'src/lib/services/drizzleFetchService.ts',
      'src/lib/services/drizzleUnifiedSearchService.ts'
    ];

    let allGood = true;

    for (const file of serverOnlyFiles) {
      if (existsSync(file)) {
        const content = readFileSync(file, 'utf-8');
        
        // 检查是否包含 postgres 导入
        if (content.includes("import postgres from 'postgres'") || 
            content.includes('from "postgres"')) {
          console.log(`❌ ${file}: 仍然包含 postgres 导入`);
          allGood = false;
        } else if (content.includes("from './drizzle'") || 
                   content.includes("from '@/lib/drizzle'")) {
          console.log(`❌ ${file}: 仍然导入 drizzle.ts`);
          allGood = false;
        } else {
          console.log(`✅ ${file}: 导入正确`);
        }
      }
    }

    if (allGood) {
      console.log('\n🎉 所有导入修复完成！');
      console.log('现在可以安全地运行 npm run dev');
    } else {
      console.log('\n⚠️  仍有问题需要手动修复');
    }
  }
}

// 运行修复
if (require.main === module) {
  const fixer = new ImportFixer();
  fixer.fixAllImports();
}
