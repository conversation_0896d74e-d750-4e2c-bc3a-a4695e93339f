# Drizzle搜索架构重构文档

## 🎯 重构目标

本次重构完全移除了Prisma依赖，统一使用Drizzle ORM，并解决了Active Filters重复显示的问题，同时增加了多数据库服务器支持。

## 🔧 主要变更

### 1. 修复Active Filters重复显示问题

**问题原因**：
- `UnifiedDatabasePageContent.tsx` 中的 `getCurrentFilters()` 函数将所有 `searchState.conditions` 都当作filter条件
- 然后又单独遍历 `searchState.conditions` 生成advanced search chips
- 导致同一个条件被显示两次

**解决方案**：
- 重新设计了条件显示逻辑，直接从 `searchState.conditions` 生成chips
- 根据字段配置（`isFilterable`）判断显示类型，避免重复

**修改文件**：
- `src/app/data/list/[database]/UnifiedDatabasePageContent.tsx`

### 2. 完全移除Prisma依赖

**新增文件**：
- `src/lib/database-servers.ts` - 多数据库服务器配置管理
- `src/lib/services/drizzleSearchService.ts` - 基于Drizzle的统一搜索服务
- `src/app/api/drizzle-search/[database]/route.ts` - 新的搜索API端点
- `src/app/api/drizzle-statistics/[database]/[field]/route.ts` - 字段统计API
- `src/config/database-servers.json` - 数据库服务器配置文件

**修改文件**：
- `src/lib/hooks/useUnifiedSearch.ts` - 更新为使用新的Drizzle API
- `src/app/data/list/[database]/page.tsx` - 切换到UnifiedDatabasePageContent

### 3. 多数据库服务器支持

**新功能**：
- 支持配置多个数据库服务器
- 支持不同数据库映射到不同服务器
- 动态连接池管理
- 配置文件驱动的服务器选择

## 🚀 使用方法

### 1. 配置数据库服务器

编辑 `src/config/database-servers.json`：

```json
{
  "servers": [
    {
      "id": "default",
      "name": "Default PostgreSQL Server",
      "host": "localhost",
      "port": 5432,
      "database": "medical_devices",
      "username": "postgres",
      "password": "password",
      "isActive": true
    },
    {
      "id": "us_server",
      "name": "US Medical Device Server",
      "host": "*************",
      "port": 5432,
      "database": "us_medical_devices",
      "username": "us_user",
      "password": "us_password",
      "isActive": true
    }
  ],
  "mappings": [
    {
      "databaseCode": "us_pmn",
      "serverId": "us_server",
      "tableName": "us_pmn",
      "isActive": true
    }
  ]
}
```

### 2. 环境变量配置

可以通过环境变量覆盖配置文件中的密码：

```bash
DB_PASSWORD_DEFAULT=your_default_password
DB_PASSWORD_US_SERVER=your_us_server_password
```

### 3. API使用

**搜索API**：
```typescript
// POST /api/drizzle-search/us_pmn
{
  "conditions": [
    {
      "id": "1",
      "field": "devicename",
      "value": "dental"
    }
  ],
  "globalKeyword": "dental",
  "page": 1,
  "limit": 20,
  "sortBy": "decisiondate",
  "sortOrder": "desc"
}
```

**统计API**：
```typescript
// POST /api/drizzle-statistics/us_pmn/devicename
{
  "conditions": [],
  "limit": 50
}
```

## 🧪 测试验证

### 运行测试脚本

```bash
# 测试Drizzle搜索功能
npx tsx scripts/test-drizzle-search.ts

# 验证迁移完整性
npx tsx scripts/migrate-to-drizzle.ts
```

### 手动测试步骤

1. **访问页面**：`http://localhost:3000/data/list/us_pmn`
2. **测试搜索**：在device name字段搜索"dental"
3. **验证Active Filters**：确认只显示一个"device name: dental"标签
4. **测试Advanced Search**：使用高级搜索功能
5. **验证统计**：检查filter面板中的统计数据

## 📊 性能优化

### 1. 连接池管理
- 每个服务器维护独立的连接池
- 支持配置最大连接数、空闲超时等参数
- 自动连接复用和清理

### 2. 统计数据缓存
- 字段统计数据缓存在内存中
- 支持条件变化时自动更新
- 减少重复查询

### 3. ES集成
- 全局关键词搜索优先使用Elasticsearch
- ES失败时自动回退到数据库搜索
- 支持ES搜索结果与数据库条件的组合

## 🔄 迁移步骤

### 1. 备份数据
确保数据库有完整备份

### 2. 更新代码
```bash
git pull origin main
npm install
```

### 3. 配置服务器
编辑 `src/config/database-servers.json`

### 4. 测试验证
```bash
npx tsx scripts/migrate-to-drizzle.ts
```

### 5. 重启应用
```bash
npm run dev
```

## 🐛 故障排除

### 常见问题

1. **连接失败**
   - 检查数据库服务器配置
   - 验证网络连接
   - 确认用户权限

2. **搜索结果为空**
   - 检查表名映射配置
   - 验证字段名称
   - 查看服务器日志

3. **统计数据不准确**
   - 清除统计缓存
   - 检查字段配置
   - 验证数据库索引

### 日志查看

```bash
# 查看搜索日志
grep "DrizzleSearch" logs/app.log

# 查看连接日志
grep "DatabaseConnection" logs/app.log
```

## 📈 后续扩展

### 1. 支持更多数据库类型
- MySQL支持
- SQLite支持
- 其他PostgreSQL兼容数据库

### 2. 高级功能
- 读写分离
- 数据库负载均衡
- 自动故障转移

### 3. 监控和告警
- 连接池监控
- 查询性能监控
- 错误率告警

## 🤝 贡献指南

1. 创建功能分支
2. 编写测试用例
3. 更新文档
4. 提交Pull Request

## 📞 支持

如有问题，请：
1. 查看本文档
2. 运行测试脚本
3. 检查日志文件
4. 联系开发团队
