import Redis from 'ioredis';
import { db } from './prisma';

// Redis客户端配置
const redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6379');

// 缓存键前缀
const CACHE_PREFIX = 'db_config:';
const FIELD_CONFIG_KEY = (databaseCode: string) => `${CACHE_PREFIX}fields:${databaseCode}`;
const ALL_CONFIG_KEY = (databaseCode: string) => `${CACHE_PREFIX}all:${databaseCode}`;

// 缓存TTL（5分钟）
const CACHE_TTL = 300;

// 配置类型定义
export interface DatabaseFieldConfig {
  fieldName: string;
  displayName: string;
  fieldType: 'text' | 'date' | 'number' | 'boolean' | 'select' | 'json';
  isVisible: boolean;
  isSearchable: boolean;
  isFilterable: boolean;
  isAdvancedSearchable?: boolean;
  isSortable: boolean;
  sortOrder: number;
  listOrder: number;
  detailOrder: number;
  searchType: 'exact' | 'contains' | 'range' | 'date_range' | 'starts_with' | 'ends_with';
  filterType: 'select' | 'input' | 'date_range' | 'checkbox' | 'multi_select' | 'range';
  validationRules?: Record<string, unknown>;
  options?: Record<string, unknown>;
  todetail?: boolean;

  // 统计配置
  isStatisticsEnabled?: boolean;
  statisticsOrder?: number;
  statisticsType?: 'count' | 'sum' | 'avg' | 'min_max' | 'group_by';
  statisticsDisplayName?: string;
  statisticsSortOrder?: 'asc' | 'desc';
  statisticsDefaultLimit?: number;
  statisticsMaxLimit?: number;
  statisticsConfig?: Record<string, unknown>;

  // 导出配置
  isExportable?: boolean;
  exportOrder?: number;
  exportDisplayName?: string;
}

export interface DatabaseSortConfig {
  field: string;
  order: 'asc' | 'desc';
}

export interface DatabaseConfig {
  fields: DatabaseFieldConfig[];
  defaultSort?: DatabaseSortConfig[];
}

// 缓存服务类
export class ConfigCacheService {
  // 获取字段配置
  static async getFieldConfigs(databaseCode: string): Promise<DatabaseFieldConfig[]> {
    const cacheKey = FIELD_CONFIG_KEY(databaseCode);
    
    try {
      // 尝试从缓存获取
      const cached = await redis.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }
    } catch (__error) {
      console.warn('Redis缓存读取失败，从数据库获取配置:', __error);
    }

    // 从数据库获取
    const fieldConfigs = await db.fieldConfig.findMany({
      where: {
        databaseCode,
        isActive: true,
      },
      orderBy: [
        { listOrder: 'asc' },
        { fieldName: 'asc' },
      ],
      select: {
        fieldName: true,
        displayName: true,
        fieldType: true,
        isVisible: true,
        isSearchable: true,
        isFilterable: true,
        isAdvancedSearchable: true,
        isSortable: true,
        sortOrder: true,
        listOrder: true,
        detailOrder: true,
        searchType: true,
        filterType: true,
        validationRules: true,
        options: true,
        todetail: true,
        // 统计配置字段
        isStatisticsEnabled: true,
        statisticsOrder: true,
        statisticsType: true,
        statisticsDisplayName: true,
        statisticsSortOrder: true,
        statisticsDefaultLimit: true,
        statisticsMaxLimit: true,
        statisticsConfig: true,
        // 导出配置字段
        isExportable: true,
        exportOrder: true,
        exportDisplayName: true,
      },
    });

    const configs = fieldConfigs.map((config: Record<string, unknown>) => ({
      fieldName: config.fieldName as string,
      displayName: config.displayName as string,
      fieldType: config.fieldType as 'text' | 'number' | 'boolean' | 'date' | 'select' | 'json',
      isVisible: config.isVisible as boolean,
      isSearchable: config.isSearchable as boolean,
      isFilterable: config.isFilterable as boolean,
      isAdvancedSearchable: (config.isAdvancedSearchable as boolean) || false,
      isSortable: config.isSortable as boolean,
      sortOrder: config.sortOrder as number,
      listOrder: config.listOrder as number,
      detailOrder: config.detailOrder as number,
      searchType: config.searchType as 'exact' | 'contains' | 'starts_with' | 'ends_with' | 'range' | 'date_range',
      filterType: config.filterType as 'input' | 'select' | 'multi_select' | 'checkbox' | 'range' | 'date_range',
      validationRules: config.validationRules as Record<string, unknown> | undefined,
      options: config.options as Record<string, unknown> | undefined,
      todetail: (config.todetail as boolean) || false,

      // 统计配置
      isStatisticsEnabled: (config.isStatisticsEnabled as boolean) || false,
      statisticsOrder: (config.statisticsOrder as number) || 0,
      statisticsType: (config.statisticsType as 'count' | 'sum' | 'avg' | 'min_max' | 'group_by' | undefined) || 'count',
      statisticsDisplayName: config.statisticsDisplayName as string | undefined,
      statisticsSortOrder: (config.statisticsSortOrder as 'asc' | 'desc' | undefined) || 'desc',
      statisticsDefaultLimit: (config.statisticsDefaultLimit as number) || 5,
      statisticsMaxLimit: (config.statisticsMaxLimit as number) || 50,
      statisticsConfig: config.statisticsConfig as Record<string, unknown> | undefined,

      // 导出配置
      isExportable: (config.isExportable as boolean) !== false, // 默认为true
      exportOrder: (config.exportOrder as number) || 0,
      exportDisplayName: config.exportDisplayName as string | undefined,
    }));

    // 缓存结果
    try {
      await redis.setex(cacheKey, CACHE_TTL, JSON.stringify(configs));
    } catch (__error) {
      console.warn('Redis缓存写入失败:', __error);
    }

    return configs;
  }



  // 获取完整数据库配置
  static async getDatabaseConfig(databaseCode: string): Promise<DatabaseConfig> {
    const cacheKey = ALL_CONFIG_KEY(databaseCode);
    
    try {
      // 尝试从缓存获取
      const cached = await redis.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }
    } catch (__error) {
      console.warn('Redis缓存读取失败，从数据库获取配置:', __error);
    }

    // 获取字段配置
    const fields = await this.getFieldConfigs(databaseCode);

    // 获取数据库配置（只获取默认排序，翻页配置使用全局设置）
    const dbConfig = await db.databaseConfig.findUnique({
      where: { code: databaseCode },
      select: {
        defaultSort: true
      }
    });

    // 解析默认排序配置
    let defaultSort: DatabaseSortConfig[] | undefined;
    if (dbConfig?.defaultSort) {
      try {
        defaultSort = dbConfig.defaultSort as unknown as DatabaseSortConfig[];
      } catch (__error) {
        console.warn(`解析 ${databaseCode} 默认排序配置失败:`, __error);
      }
    }

    const config = {
      fields,
      defaultSort,
      // 移除翻页配置，使用全局设置以提升性能
    };

    // 缓存结果
    try {
      await redis.setex(cacheKey, CACHE_TTL, JSON.stringify(config));
    } catch (__error) {
      console.warn('Redis缓存写入失败:', __error);
    }

    return config;
  }

  // 清除数据库配置缓存
  static async clearDatabaseCache(databaseCode: string): Promise<void> {
    const keys = [
      FIELD_CONFIG_KEY(databaseCode),
      ALL_CONFIG_KEY(databaseCode),
    ];

    try {
      await redis.del(...keys);
    } catch (__error) {
      console.warn('Redis缓存清除失败:', __error);
    }
  }

  // 清除所有配置缓存
  static async clearAllCache(): Promise<void> {
    try {
      const keys = await redis.keys(`${CACHE_PREFIX}*`);
      if (keys.length > 0) {
        await redis.del(...keys);
      }
    } catch (__error) {
      console.warn('Redis缓存清除失败:', __error);
    }
  }

  // 预热缓存
  static async warmupCache(databaseCodes: string[]): Promise<void> {
    const promises = databaseCodes.map(code => this.getDatabaseConfig(code));
    await Promise.allSettled(promises);
  }
}



// 移除硬编码默认配置，完全依赖数据库配置表



// 获取数据库配置（完全依赖配置表）
export async function getDatabaseConfig(databaseCode: string): Promise<DatabaseConfig> {
  try {
    // 从配置表获取配置
    const config = await ConfigCacheService.getDatabaseConfig(databaseCode);
    return config;
  } catch (__error) {
    console.error(`获取数据库配置失败 ${databaseCode}:`, __error);
    // 不再使用硬编码回退，返回空配置
    return {
      fields: [],
    };
  }
}

/**
 * 配置验证函数
 */
export function validateFieldConfig(config: DatabaseFieldConfig): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  // 验证必填字段
  if (!config.fieldName) {
    errors.push('fieldName 是必填字段');
  }
  if (!config.displayName) {
    errors.push('displayName 是必填字段');
  }

  // 验证 filterType 和 fieldType 的兼容性
  if (config.filterType === 'multi_select' && config.fieldType === 'boolean') {
    errors.push('布尔字段不应该使用 multi_select 筛选类型');
  }

  if (config.filterType === 'date_range' && config.fieldType !== 'date') {
    errors.push('date_range 筛选类型只能用于日期字段');
  }

  // 验证排序值
  if (config.sortOrder < 0 || config.listOrder < 0 || config.detailOrder < 0) {
    errors.push('排序值不能为负数');
  }

  // 验证筛选类型
  const validFilterTypes = ['select', 'multi_select', 'input', 'date_range', 'checkbox', 'range'];
  if (config.filterType && !validFilterTypes.includes(config.filterType)) {
    errors.push(`无效的筛选类型: ${config.filterType}`);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 批量验证字段配置
 */
export function validateDatabaseConfig(config: DatabaseConfig): {
  isValid: boolean;
  errors: string[];
  fieldErrors: Record<string, string[]>;
} {
  const errors: string[] = [];
  const fieldErrors: Record<string, string[]> = {};

  // 验证字段配置
  config.fields.forEach((field, _index) => {
    const validation = validateFieldConfig(field);
    if (!validation.isValid) {
      fieldErrors[field.fieldName || `field_${_index}`] = validation.errors;
    }
  });

  // 检查字段名重复
  const fieldNames = config.fields.map(f => f.fieldName).filter(Boolean);
  const duplicates = fieldNames.filter((name, _index) => fieldNames.indexOf(name) !== _index);
  if (duplicates.length > 0) {
    errors.push(`重复的字段名: ${duplicates.join(', ')}`);
  }

  return {
    isValid: errors.length === 0 && Object.keys(fieldErrors).length === 0,
    errors,
    fieldErrors
  };
}

// 导出Redis客户端（用于其他模块）
export { redis };