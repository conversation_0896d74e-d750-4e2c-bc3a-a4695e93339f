// import { db } from '@/lib/prisma'; // 未使用
import { getDynamicModel, isPrismaModel } from '@/lib/dynamicTableMapping';
import { getDatabaseConfig } from '@/lib/configCache';
import type { DatabaseConfig } from '@/lib/configCache';

/**
 * Prisma回捞结果接口
 */
export interface PrismaFetchResult {
  table_code: string;
  data: unknown[];
  total: number;
  missing_ids: string[]; // ES有但DB无的ID
}

/**
 * 批量回捞参数接口
 */
export interface BatchFetchParams {
  id_groups: Array<{
    table_code: string;
    ids: string[];
  }>;
  filters?: Record<string, unknown>;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

/**
 * 单库回捞参数接口
 */
export interface SingleFetchParams {
  table_code: string;
  ids: string[];
  filters?: Record<string, unknown>;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

/**
 * Prisma回捞服务
 * 基于ES返回的ID集合批量查询Prisma，支持二次筛选和排序
 */
export class PrismaFetchService {
  
  /**
   * 批量回捞多个数据库的数据
   * @param params 批量回捞参数
   * @returns 按数据库分组的回捞结果
   */
  static async batchFetch(params: BatchFetchParams): Promise<PrismaFetchResult[]> {
    const { id_groups, filters = {}, sort_by, sort_order = 'desc' } = params;
    
    console.log('[PrismaFetchService] 批量回捞:', { 
      groupCount: id_groups.length, 
      totalIds: id_groups.reduce((sum, group) => sum + group.ids.length, 0)
    });
    
    const results: PrismaFetchResult[] = [];
    
    // 并行处理每个数据库
    const fetchPromises = id_groups.map(async (group) => {
      try {
        const result = await this.singleFetch({
          table_code: group.table_code,
          ids: group.ids,
          filters,
          sort_by,
          sort_order
        });
        
        return result;
      } catch (error) {
        console.error(`[PrismaFetchService] 数据库 ${group.table_code} 回捞失败:`, error);
        
        // 返回空结果，但记录所有ID为缺失
        return {
          table_code: group.table_code,
          data: [],
          total: 0,
          missing_ids: group.ids
        };
      }
    });
    
    const fetchResults = await Promise.all(fetchPromises);
    results.push(...fetchResults);
    
    console.log('[PrismaFetchService] 批量回捞完成:', {
      resultCount: results.length,
      totalData: results.reduce((sum, r) => sum + r.data.length, 0),
      totalMissing: results.reduce((sum, r) => sum + r.missing_ids.length, 0)
    });
    
    return results;
  }
  
  /**
   * 单个数据库回捞
   * @param params 单库回捞参数
   * @returns 回捞结果
   */
  static async singleFetch(params: SingleFetchParams): Promise<PrismaFetchResult> {
    const { 
      table_code, 
      ids, 
      filters = {}, 
      sort_by, 
      sort_order = 'desc',
      page = 1,
      limit = 20
    } = params;
    
    console.log('[PrismaFetchService] 单库回捞:', { table_code, idCount: ids.length, page, limit });
    
    try {
      // 获取动态模型
      const model = await getDynamicModel(table_code);
      if (!isPrismaModel(model)) {
        throw new Error(`无效的Prisma模型: ${table_code}`);
      }
      
      // 获取数据库配置
      const config: DatabaseConfig = await getDatabaseConfig(table_code);
      const visibleFields = config.fields.filter(f => f.isVisible).map(f => f.fieldName);
      const sortableFields = config.fields.filter(f => f.isSortable).map(f => f.fieldName);
      
      // 构建查询条件
      const where: any = {
        id: { in: ids }
      };
      
      // 添加额外筛选条件
      this.applyFilters(where, filters, config);
      
      // 构建排序条件
      let orderBy: any = {};
      if (sort_by && sortableFields.includes(sort_by)) {
        orderBy[sort_by] = sort_order;
      } else if (config.defaultSort && config.defaultSort.length > 0) {
        // 使用配置中的默认排序
        if (config.defaultSort.length === 1) {
          const sort = config.defaultSort[0];
          orderBy = { [sort.field]: sort.order };
        } else {
          // 多字段排序
          orderBy = config.defaultSort.map(sort => ({
            [sort.field]: sort.order
          }));
        }
      } else {
        // 默认按ID排序
        orderBy = { id: sort_order };
      }
      
      // 构建select对象
      const select: Record<string, boolean> = {};
      visibleFields.forEach(f => { select[f] = true; });
      select['id'] = true; // 主键始终返回
      
      // 分页计算
      const skip = (page - 1) * limit;
      
      // 执行查询
      const [data, total] = await Promise.all([
        model.findMany({
          where,
          orderBy,
          skip,
          take: limit,
          select,
        }),
        (model as any).count({ where })
      ]);
      
      // 找出缺失的ID
      const foundIds = new Set((data as any[]).map(item => item.id));
      const missing_ids = ids.filter(id => !foundIds.has(id));
      
      console.log('[PrismaFetchService] 单库回捞完成:', {
        table_code,
        found: (data as any[]).length,
        total,
        missing: missing_ids.length
      });
      
      return {
        table_code,
        data: data as unknown[],
        total,
        missing_ids
      };
      
    } catch (error) {
      console.error(`[PrismaFetchService] 单库回捞失败 ${table_code}:`, error);
      throw error;
    }
  }
  
  /**
   * 应用筛选条件到where子句
   * @param where Prisma where对象
   * @param filters 筛选条件
   * @param config 数据库配置
   */
  private static applyFilters(
    where: any, 
    filters: Record<string, unknown>, 
    config: DatabaseConfig
  ): void {
    const filterableFields = config.fields.filter(f => f.isFilterable);
    
    for (const [key, value] of Object.entries(filters)) {
      if (value === undefined || value === null || value === '') {
        continue;
      }
      
      // 跳过allFields，这个由ES处理
      if (key === 'allFields') {
        continue;
      }
      
      const fieldConfig = filterableFields.find(f => f.fieldName === key);
      if (!fieldConfig) {
        continue;
      }
      
      // 根据字段类型应用不同的筛选逻辑
      switch (fieldConfig.fieldType) {
        case 'text':
          if (typeof value === 'string') {
            where[key] = { contains: value, mode: 'insensitive' };
          }
          break;

        case 'date':
          if (typeof value === 'object' && value !== null) {
            const dateFilter = value as { from?: string; to?: string };
            if (dateFilter.from || dateFilter.to) {
              where[key] = {};
              if (dateFilter.from) {
                where[key].gte = new Date(dateFilter.from);
              }
              if (dateFilter.to) {
                where[key].lte = new Date(dateFilter.to);
              }
            }
          }
          break;
          
        case 'number':
          if (typeof value === 'number') {
            where[key] = value;
          } else if (typeof value === 'object' && value !== null) {
            const numFilter = value as { from?: number; to?: number };
            if (numFilter.from !== undefined || numFilter.to !== undefined) {
              where[key] = {};
              if (numFilter.from !== undefined) {
                where[key].gte = numFilter.from;
              }
              if (numFilter.to !== undefined) {
                where[key].lte = numFilter.to;
              }
            }
          }
          break;
          
        case 'boolean':
          if (typeof value === 'boolean') {
            where[key] = value;
          }
          break;
          
        default:
          // 默认精确匹配
          where[key] = value;
          break;
      }
    }
  }
  
  /**
   * 记录缺失的ID，用于触发同步任务
   * @param missing_ids 缺失的ID列表
   * @param table_code 数据库代码
   */
  static async logMissingIds(missing_ids: string[], table_code: string): Promise<void> {
    if (missing_ids.length === 0) return;
    
    console.warn(`[PrismaFetchService] 发现缺失ID: ${table_code}`, {
      count: missing_ids.length,
      sample: missing_ids.slice(0, 5)
    });
    
    // TODO: 这里可以实现同步任务触发逻辑
    // 例如：写入同步队列、发送通知等
  }
}
