import React from 'react';
import { X, Search, Filter, Tag } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

// 搜索条件类型定义
export interface SearchChip {
  id: string;
  type: 'query' | 'filter' | 'advanced' | 'sort';
  label: string;
  value: string | number | boolean;
  displayValue: string;
  field?: string; // 字段名
  operator?: string; // 操作符
  removable?: boolean; // 是否可移除
  color?: 'default' | 'secondary' | 'destructive' | 'outline';
}

export interface SearchChipsProps {
  chips: SearchChip[];
  onRemoveChip: (chipId: string) => void;
  onClearAll?: () => void;
  className?: string;
  showClearAll?: boolean;
  maxDisplay?: number; // 最大显示数量
  compact?: boolean; // 紧凑模式
}

// 根据类型获取图标
const getChipIcon = (type: SearchChip['type']) => {
  switch (type) {
    case 'query':
      return <Search className="h-3 w-3" />;
    case 'filter':
      return <Filter className="h-3 w-3" />;
    case 'advanced':
      return <Tag className="h-3 w-3" />;
    case 'sort':
      return <Filter className="h-3 w-3" />;
    default:
      return <Tag className="h-3 w-3" />;
  }
};

// 根据类型获取颜色
const getChipVariant = (type: SearchChip['type'], color?: SearchChip['color']): 'default' | 'secondary' | 'destructive' | 'outline' => {
  if (color) return color;

  switch (type) {
    case 'query':
      return 'default';
    case 'filter':
      return 'secondary';
    case 'advanced':
      return 'outline';
    case 'sort':
      return 'outline';
    default:
      return 'secondary';
  }
};

export default function SearchChips({
  chips,
  onRemoveChip,
  onClearAll,
  className,
  showClearAll = true,
  maxDisplay,
  compact = false
}: SearchChipsProps) {
  // 过滤可移除的标签
  const removableChips = chips.filter(chip => chip.removable !== false);
  const displayChips = maxDisplay ? removableChips.slice(0, maxDisplay) : removableChips;
  const hiddenCount = maxDisplay && removableChips.length > maxDisplay 
    ? removableChips.length - maxDisplay 
    : 0;

  if (removableChips.length === 0) {
    return null;
  }

  return (
    <div className={cn(
      "flex flex-wrap items-center gap-2 px-3 py-2 bg-gray-50 border-b border-gray-200",
      compact && "px-2 py-1.5 gap-1",
      className
    )}>
      {/* 标题 */}
      <span className={cn(
        "text-sm font-medium text-gray-600 flex-shrink-0",
        compact && "text-xs"
      )}>
        Active Filters:
      </span>

      {/* 搜索条件标签 */}
      <div className="flex flex-wrap items-center gap-1">
        {displayChips.map((chip) => (
          <Badge
            key={chip.id}
            variant={getChipVariant(chip.type, chip.color)}
            className={cn(
              "flex items-center gap-1 pr-0.5 max-w-xs text-xs py-1 px-2",
              compact && "text-xs py-0.5 px-1.5"
            )}
          >
            {!compact && getChipIcon(chip.type)}
            <span className="truncate" title={chip.displayValue}>
              {chip.displayValue}
            </span>
            <Button
              variant="ghost"
              size="sm"
              className={cn(
                "h-3.5 w-3.5 p-0 hover:bg-gray-200 rounded-full ml-0.5",
                compact && "h-3 w-3"
              )}
              onClick={() => onRemoveChip(chip.id)}
              title={`Remove ${chip.label}`}
            >
              <X className={cn("h-2.5 w-2.5", compact && "h-2 w-2")} />
            </Button>
          </Badge>
        ))}

        {/* 显示隐藏数量 */}
        {hiddenCount > 0 && (
          <Badge variant="outline" className={cn(
            "text-gray-500",
            compact && "text-xs py-0.5 px-2"
          )}>
            +{hiddenCount} more
          </Badge>
        )}
      </div>

      {/* 清除所有按钮 */}
      {showClearAll && onClearAll && (
        <Button
          variant="ghost"
          size="sm"
          onClick={onClearAll}
          className={cn(
            "text-gray-500 hover:text-gray-700 ml-auto flex-shrink-0 text-xs px-2 py-1 h-auto",
            compact && "text-xs px-1.5 py-0.5"
          )}
        >
          Clear All
        </Button>
      )}
    </div>
  );
}
