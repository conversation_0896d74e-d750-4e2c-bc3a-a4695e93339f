"use client";

import Link from "next/link";
import { useEffect, useState } from "react";

export interface BreadcrumbItem {
  label: string;
  href?: string;
  isActive?: boolean;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
}

export default function Breadcrumb({ items, className ="" }: BreadcrumbProps) {
  return (
    <nav
      className={`flex items-center space-x-0.5 text-xs text-gray-500 py-1.5 leading-tight bg-gray-50 border-b border-gray-100 ${className}`}
      aria-label="Breadcrumb">
      <div className="w-full px-4 flex items-center space-x-0.5">
        {items.map((item, index) => (
          <div key={index} className="flex items-center">
            {index > 0 && (
              <span className="text-gray-400 mx-1.5 text-xs">›</span>
            )}
            {item.href && !item.isActive ? (
              <Link
                href={item.href}
                className="hover:text-blue-600 transition-colors duration-200 text-xs max-w-32 truncate inline-block" title={item.label}
              >
                {item.label}
              </Link>
            ) : (
              <span
                className={`text-xs max-w-40 truncate inline-block ${item.isActive ? "text-gray-700 font-medium" : "text-gray-500"}`}
                title={item.label}
              >
                {item.label}
              </span>
            )}
          </div>
        ))}
      </div>
    </nav>
  );
}

// Hook to generate breadcrumb items for database detail pages
export function useDatabaseBreadcrumb(database: string, itemTitle?: string) {
  const [breadcrumbItems, setBreadcrumbItems] = useState<BreadcrumbItem[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const generateBreadcrumb = async () => {
      try {
        // Fetch database configuration to get category and name
        const response = await fetch('/api/config/databases');
        const result = await response.json();
        
        if (result.success && result.data[database]) {
          const dbConfig = result.data[database];
          
          const items: BreadcrumbItem[] = [
            {
              label: "Home",
              href: "/"
            },
            {
              label: dbConfig.category || "Database",
              href: "/" // Could be enhanced to link to category page
            },
            {
              label: dbConfig.name,
              href: `/data/list/${database}`
            }
          ];

          // Add detail item if provided (truncate long titles)
          if (itemTitle) {
            const truncatedTitle = itemTitle.length > 50
              ? itemTitle.substring(0, 50) + "..."
              : itemTitle;
            items.push({
              label: truncatedTitle,
              isActive: true
            });
          }

          setBreadcrumbItems(items);
        } else {
          // Fallback breadcrumb if config fetch fails
          const items: BreadcrumbItem[] = [
            {
              label: "Home",
              href: "/"
            },
            {
              label: "Database",
              href: "/"
            },
            {
              label: database.toUpperCase(),
              href: `/data/list/${database}`
            }
          ];

          if (itemTitle) {
            const truncatedTitle = itemTitle.length > 50
              ? itemTitle.substring(0, 50) + "..."
              : itemTitle;
            items.push({
              label: truncatedTitle,
              isActive: true
            });
          }

          setBreadcrumbItems(items);
        }
      } catch (error) {
        console.error('Error generating breadcrumb:', error);
        // Minimal fallback
        setBreadcrumbItems([
          {
            label: "Home",
            href: "/"
          },
          {
            label: database.toUpperCase(),
            href: `/data/list/${database}`,
            isActive: !itemTitle
          },
          ...(itemTitle ? [{
            label: itemTitle.length > 50 ? itemTitle.substring(0, 50) + "..." : itemTitle,
            isActive: true
          }] : [])
        ]);
      } finally {
        setLoading(false);
      }
    };

    generateBreadcrumb();
  }, [database, itemTitle]);

  return { breadcrumbItems, loading };
}
