'use client';

import { useState, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { MultiSelect } from '@/components/ui/multi-select';
import { DateRangePicker } from '@/components/ui/date-range-picker';
import { Loader2, Filter, X } from 'lucide-react';
import { useUnifiedSearch, DatabaseFieldConfig } from '@/lib/hooks/useUnifiedSearch';

interface UnifiedFilterPanelProps {
  database: string;
  config: { fields: DatabaseFieldConfig[] } | null;
  isOpen: boolean;
  onToggle: () => void;
  className?: string;
}

interface FilterOption {
  value: string;
  count: number;
}

export function UnifiedFilterPanel({
  database,
  config,
  isOpen,
  onToggle,
  className = ''
}: UnifiedFilterPanelProps) {
  const [pendingFilters, setPendingFilters] = useState<Record<string, any>>({});
  const [dynamicCounts, setDynamicCounts] = useState<Record<string, FilterOption[]>>({});

  // 使用统一搜索Hook
  const {
    searchState,
    updateFromFilterPanel,
    getFieldStatistics,
    clearAllConditions
  } = useUnifiedSearch(database, config);

  // 获取可筛选的字段
  const filterableFields = useMemo(() => {
    if (!config) return [];
    return config.fields
      .filter(field => field.isFilterable)
      .sort((a, b) => a.filterOrder - b.filterOrder);
  }, [config]);

  // 获取字段的动态计数
  const fetchFieldCounts = async (fieldName: string) => {
    try {
      const response = await fetch(`/api/stats/${database}/field-statistics`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          fieldName,
          conditions: searchState.conditions.filter(c => c.field !== fieldName), // 排除当前字段
          limit: 100
        }),
      });

      const result = await response.json();
      
      if (result.success) {
        setDynamicCounts(prev => ({
          ...prev,
          [fieldName]: result.data.items || []
        }));
      }
    } catch (error) {
      console.error(`获取字段 ${fieldName} 统计失败:`, error);
    }
  };

  // 初始化时获取所有可筛选字段的统计数据
  useEffect(() => {
    if (filterableFields.length > 0) {
      filterableFields.forEach(field => {
        if (field.filterType === 'multi_select' || field.filterType === 'select' || field.filterType === 'checkbox') {
          fetchFieldCounts(field.fieldName);
        }
      });
    }
  }, [filterableFields, database]);

  // 当搜索条件变化时，更新动态计数
  useEffect(() => {
    if (searchState.conditions.length > 0) {
      filterableFields.forEach(field => {
        if (field.filterType === 'multi_select' || field.filterType === 'select' || field.filterType === 'checkbox') {
          fetchFieldCounts(field.fieldName);
        }
      });
    }
  }, [searchState.conditions]);

  // 处理筛选条件变化
  const handleFilterChange = (fieldName: string, value: any) => {
    setPendingFilters(prev => ({
      ...prev,
      [fieldName]: value
    }));
  };

  // 应用筛选条件
  const handleApplyFilters = async () => {
    await updateFromFilterPanel(pendingFilters);
  };

  // 清空筛选条件
  const handleClearFilters = async () => {
    setPendingFilters({});
    await clearAllConditions();
  };

  // 渲染单个筛选字段
  const renderFilterField = (field: DatabaseFieldConfig) => {
    const fieldName = field.fieldName;
    const currentValue = pendingFilters[fieldName];
    
    // 获取字段选项（优先使用统计数据，回退到动态计数）
    const fieldStats = getFieldStatistics(fieldName);
    const fieldCounts = dynamicCounts[fieldName] || [];
    const options = fieldStats?.items || fieldCounts;

    switch (field.filterType) {
      case 'select':
        return (
          <div key={fieldName} className="space-y-1">
            <Label className="text-xs font-medium text-gray-700">
              {field.displayName}
            </Label>
            <Select
              value={currentValue || '__all__'}
              onValueChange={(value) => handleFilterChange(fieldName, value === '__all__' ? '' : value)}
            >
              <SelectTrigger className="w-full text-sm">
                <SelectValue placeholder={`Select ${field.displayName.toLowerCase()}`} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="__all__">All</SelectItem>
                {options.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.value} ({option.count})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        );

      case 'multi_select':
      case 'checkbox':
        return (
          <div key={fieldName} className="space-y-1">
            <Label className="text-xs font-medium text-gray-700">
              {field.displayName}
            </Label>
            <MultiSelect
              options={options.map(option => ({
                value: option.value,
                label: field.filterType === 'checkbox' 
                  ? (option.value === 'true' ? 'Yes' : option.value === 'false' ? 'No' : option.value)
                  : option.value,
                count: option.count
              }))}
              value={currentValue || []}
              onValueChange={(value) => handleFilterChange(fieldName, value)}
              placeholder={`Select ${field.displayName}`}
            />
          </div>
        );

      case 'date_range':
        return (
          <div key={fieldName} className="space-y-1">
            <Label className="text-xs font-medium text-gray-700">
              {field.displayName}
            </Label>
            <DateRangePicker
              value={currentValue}
              onChange={(value) => handleFilterChange(fieldName, value)}
              placeholder={`Select ${field.displayName.toLowerCase()} range`}
            />
          </div>
        );

      case 'input':
        return (
          <div key={fieldName} className="space-y-1">
            <Label className="text-xs font-medium text-gray-700">
              {field.displayName}
            </Label>
            <input
              type="text"
              value={currentValue || ''}
              onChange={(e) => handleFilterChange(fieldName, e.target.value)}
              placeholder={`Enter ${field.displayName.toLowerCase()}`}
              className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        );

      default:
        return null;
    }
  };

  if (!config) {
    return (
      <div className={`${isOpen ? 'w-72' : 'w-16'} transition-all duration-300 bg-gray-50 border-r border-gray-300 flex flex-col ${className}`}>
        <div className="p-4 border-b border-gray-200 bg-white shadow-sm">
          <div className="flex items-center justify-between">
            <h3 className={`font-medium text-gray-800 ${!isOpen ? 'hidden' : ''}`}>Filters</h3>
            <Button variant="ghost" size="sm" onClick={onToggle} className="hover:bg-gray-100">
              {isOpen ? <X className="h-4 w-4 text-gray-600" /> : <Filter className="h-4 w-4 text-gray-600" />}
            </Button>
          </div>
        </div>
        {isOpen && (
          <div className="flex-1 flex items-center justify-center">
            <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={`${isOpen ? 'w-72' : 'w-16'} transition-all duration-300 bg-gray-50 border-r border-gray-300 flex flex-col ${className}`}>
      {/* 固定头部 */}
      <div className="p-4 border-b border-gray-200 bg-white shadow-sm">
        <div className="flex items-center justify-between">
          <h3 className={`font-medium text-gray-800 ${!isOpen ? 'hidden' : ''}`}>Filters</h3>
          <Button variant="ghost" size="sm" onClick={onToggle} className="hover:bg-gray-100">
            {isOpen ? <X className="h-4 w-4 text-gray-600" /> : <Filter className="h-4 w-4 text-gray-600" />}
          </Button>
        </div>
      </div>

      {isOpen && (
        <div className="flex-1 flex flex-col min-h-0">
          {/* 筛选字段区域 */}
          <div className="flex-1 overflow-y-auto p-3 space-y-3 custom-scrollbar" style={{ maxHeight: 'calc(100vh - 200px)' }}>
            {filterableFields.length === 0 ? (
              <div className="text-center text-gray-500 text-sm py-8">
                No filterable fields configured
              </div>
            ) : (
              filterableFields.map(renderFilterField)
            )}
          </div>

          {/* 固定底部按钮 */}
          <div className="border-t border-gray-200 p-4 bg-white flex-shrink-0 relative z-60 shadow-lg">
            <div className="space-y-3">
              <div className="flex gap-2">
                <Button 
                  onClick={handleApplyFilters} 
                  disabled={searchState.isLoading}
                  className="flex-1"
                >
                  {searchState.isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                  Apply Filters
                </Button>
                <Button 
                  variant="ghost" 
                  onClick={handleClearFilters}
                  disabled={searchState.isLoading}
                >
                  Clear
                </Button>
              </div>
              
              {/* 状态指示器 */}
              {searchState.conditions.length > 0 && (
                <div className="text-xs text-gray-600 text-center">
                  {searchState.conditions.length} active filter{searchState.conditions.length !== 1 ? 's' : ''}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
