"use client";

import { useState, useEffect } from "react";
import type { SearchCondition } from "@/components/AdvancedSearch";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";

import {
  BarChart3,
  TrendingDown,
  Database,
  Activity,
  ChevronDown,
  ChevronUp,
  Percent
} from "lucide-react";

interface StatsData {
  basic: {
    total: number;
    active: number;
    inactive: number;
  };
  categories: Array<{ category: string; _count: { category: number } }>;
  companies: Array<{ companyName: string; _count: { companyName: number } }>;
  managementTypes: Array<{ managementType: string; _count: { managementType: number } }>;
  yearly: Array<{ year: number; count: number }>;
}

interface CollapsibleStatsPanelProps {
  database: string;
  isOpen: boolean;
  onToggle: () => void;
  className?: string;
  maxHeight?: number;
  filters?: Record<string, unknown>;
  advancedConditions?: SearchCondition[]; // 新增：Advanced Search条件
}

export default function CollapsibleStatsPanel({
  database,
  isOpen,
  onToggle: _onToggle,
  className = "",
  maxHeight = 400,
  filters = {},
  advancedConditions = []
}: CollapsibleStatsPanelProps) {
  const [stats, setStats] = useState<StatsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedCards, setExpandedCards] = useState<Set<string>>(new Set());

  const toggleCardExpansion = (cardName: string) => {
    const newExpanded = new Set(expandedCards);
    if (newExpanded.has(cardName)) {
      newExpanded.delete(cardName);
    } else {
      newExpanded.add(cardName);
    }
    setExpandedCards(newExpanded);
  };

  useEffect(() => {
    if (isOpen) {
      const fetchStats = async () => {
        try {
          setLoading(true);
          setError(null);

          // 优先使用Advanced Search条件，否则使用传统filters
          if (advancedConditions && advancedConditions.length > 0) {
            // 使用POST API with Advanced Search条件
            const response = await fetch(`/api/stats/${database}/configurable`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                conditions: advancedConditions,
                filters
              }),
            });
            const result = await response.json();

            if (result.success) {
              setStats(result.data);
            } else {
              setError(result.error || 'Failed to fetch statistics');
            }
          } else {
            // 回退到传统的GET API
            const params = new URLSearchParams();
            Object.entries(filters).forEach(([key, value]) => {
              if (value !== undefined && value !== null && value !== "") {
                if (Array.isArray(value)) {
                  value.forEach(v => params.append(key, String(v)));
                } else {
                  params.append(key, String(value));
                }
              }
            });
            const url = `/api/stats/${database}/configurable` + (params.toString() ? `?${params.toString()}` : "");
            const response = await fetch(url);
            const result = await response.json();

            if (result.success) {
              setStats(result.data);
            } else {
              setError(result.error || 'Failed to fetch statistics');
            }
          }
        } catch (__err) {
          setError('Network error, please try again later');
          console.error('Failed to fetch stats:', __err);
        } finally {
          setLoading(false);
        }
      };

      fetchStats();
    }
  }, [database, isOpen, filters, advancedConditions]);

  return (
    <div className={`transition-all duration-300 ease-in-out ${className}`}>
      {/* 可折叠的统计面板 */}
      <div 
        className={`
          overflow-hidden transition-all duration-300 ease-in-out
          ${isOpen ? 'opacity-100' : 'opacity-0'}
        `}
        style={{ maxHeight: isOpen ? maxHeight : 0 }}
      >
        <div className="space-y-6 pt-4 overflow-y-auto custom-scrollbar" style={{ maxHeight: maxHeight }}>
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {Array.from({ length: 8 }).map((_, i) => (
                <Card key={i}>
                  <CardHeader className="pb-2">
                    <Skeleton className="h-4 w-20" />
                  </CardHeader>
                  <CardContent>
                    <Skeleton className="h-8 w-16 mb-2" />
                    <Skeleton className="h-3 w-24" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : error || !stats ? (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-red-500">{error || '无法加载统计数据'}</p>
              </CardContent>
            </Card>
          ) : (
            <>
              {/* 概览统计 */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">总数据量</CardTitle>
                    <Database className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stats.basic.total.toLocaleString()}</div>
                    <p className="text-xs text-muted-foreground">
                      活跃: {stats.basic.active.toLocaleString()}
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">最近更新</CardTitle>
                    <Activity className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">-</div>
                    <p className="text-xs text-muted-foreground">
                      近30天更新
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">非活跃</CardTitle>
                    <TrendingDown className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stats.basic.inactive.toLocaleString()}</div>
                    <p className="text-xs text-muted-foreground">
                      非活跃记录
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">活跃率</CardTitle>
                    <Percent className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {stats.basic.total > 0 ? Math.round((stats.basic.active / stats.basic.total) * 100) : 0}%
                    </div>
                    <p className="text-xs text-muted-foreground">
                      活跃记录占比
                    </p>
                  </CardContent>
                </Card>
              </div>

              {/* 配置的统计项 */}
              {(stats as any).statistics && (stats as any).statistics.length > 0 && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {(stats as any).statistics.map((statField: any, _index: number) => {
                    const isExpanded = expandedCards.has(statField.fieldName);
                    // 从统计字段中读取显示数量配置
                    const defaultLimit = statField.statisticsDefaultLimit || 5;
                    const maxLimit = statField.statisticsMaxLimit || 50;
                    const totalItems = statField.data.items?.length || 0;
                    const displayLimit = isExpanded ? Math.min(maxLimit, totalItems) : Math.min(defaultLimit, totalItems);

                    return (
                      <Card key={statField.fieldName}>
                        <CardHeader>
                          <CardTitle className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <BarChart3 className="h-5 w-5" />
                              {statField.displayName}
                            </div>
                            {statField.data.type ==="group_by" && totalItems > defaultLimit && (
                              <button
                                onClick={() => toggleCardExpansion(statField.fieldName)}
                                className="text-xs text-muted-foreground hover:text-foreground transition-colors flex items-center gap-1">
                                {isExpanded ? (
                                  <>
                                    <ChevronUp className="h-3 w-3" />
                                    Collapse
                                  </>
                                ) : (
                                  <>
                                    <ChevronDown className="h-3 w-3" />
                                    Expand ({totalItems})
                                  </>
                                )}
                              </button>
                            )}
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          {statField.data.type ==="group_by" && statField.data.items && (
                            <div
                              className={`space-y-2 ${
                                isExpanded && totalItems > 8
                                  ? 'max-h-64 overflow-y-auto custom-scrollbar pr-2'
                                  : ''
                              }`}
                            >
                              {statField.data.items.slice(0, displayLimit).map((item: any, itemIndex: number) => (
                                <div key={itemIndex} className="flex items-center justify-between">
                                  <span className="text-sm truncate flex-1" title={item.name}>
                                    {item.name}
                                  </span>
                                  <Badge variant="secondary" className="ml-2 flex-shrink-0">
                                    {item.count?.toLocaleString()}
                                  </Badge>
                                </div>
                              ))}
                              {!isExpanded && totalItems > defaultLimit && (
                                <div className="text-xs text-muted-foreground text-center pt-2 border-t">
                                  {totalItems - defaultLimit} more items...
                                </div>
                              )}
                            </div>
                          )}
                        {statField.statisticsType ==="count" && (
                          <div className="text-center">
                            <div className="text-2xl font-bold">
                              {statField.data.items ? statField.data.items.length.toLocaleString() : '0'}
                            </div>
                            <p className="text-xs text-muted-foreground">
                              {statField.displayName}
                            </p>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                    );
                  })}
                </div>
              )}

              {/* 如果没有配置统计项，显示提示 */}
              {(!(stats as any).statistics || (stats as any).statistics.length === 0) && (
                <Card>
                  <CardContent className="p-6 text-center">
                    <p className="text-muted-foreground">暂无配置的统计项目</p>
                  </CardContent>
                </Card>
              )}

              {/* 数据概览 */}
              <Card>
                <CardHeader>
                  <CardTitle>数据概览</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {stats.basic.total.toLocaleString()}
                      </div>
                      <div className="text-sm text-muted-foreground">总记录数</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {stats.basic.active.toLocaleString()}
                      </div>
                      <div className="text-sm text-muted-foreground">活跃记录</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-600">
                        {stats.basic.inactive.toLocaleString()}
                      </div>
                      <div className="text-sm text-muted-foreground">非活跃记录</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-orange-600">
                        {stats.basic.total > 0 ? Math.round((stats.basic.active / stats.basic.total) * 100) : 0}%
                      </div>
                      <div className="text-sm text-muted-foreground">活跃率</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </div>
      </div>
    </div>
  );
} 