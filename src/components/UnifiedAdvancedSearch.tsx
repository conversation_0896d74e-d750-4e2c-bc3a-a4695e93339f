'use client';

import { useState, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { DateRangePicker } from '@/components/ui/date-range-picker';
import { MultiSelect } from '@/components/ui/multi-select';
import { Filter, Plus, X, Search, Loader2 } from 'lucide-react';
import { useUnifiedSearch, DatabaseFieldConfig } from '@/lib/hooks/useUnifiedSearch';
import { SearchCondition } from '@/lib/api';

interface UnifiedAdvancedSearchProps {
  database: string;
  config: { fields: DatabaseFieldConfig[] } | null;
  className?: string;
}

const LOGIC_OPERATORS = [
  { value: 'AND', label: 'AND' },
  { value: 'OR', label: 'OR' },
  { value: 'NOT', label: 'NOT' },
];

export function UnifiedAdvancedSearch({
  database,
  config,
  className = ''
}: UnifiedAdvancedSearchProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [localConditions, setLocalConditions] = useState<SearchCondition[]>([]);

  // 使用统一搜索Hook
  const {
    searchState,
    updateFromAdvancedSearch,
    getFieldStatistics,
    clearAllConditions
  } = useUnifiedSearch(database, config);

  // 获取可用于Advanced Search的字段
  const advancedSearchableFields = useMemo(() => {
    if (!config) return [];
    return config.fields
      .filter(field => field.isAdvancedSearchable)
      .sort((a, b) => (a.advancedSearchOrder || 0) - (b.advancedSearchOrder || 0));
  }, [config]);

  // 当对话框打开时，同步当前搜索状态到本地条件
  const handleDialogOpen = (open: boolean) => {
    if (open) {
      setLocalConditions([...searchState.conditions]);
    }
    setIsOpen(open);
  };

  // 添加新条件
  const addCondition = () => {
    if (advancedSearchableFields.length === 0) return;

    const newCondition: SearchCondition = {
      id: `condition_${Date.now()}`,
      field: advancedSearchableFields[0].fieldName,
      value: '',
      logic: localConditions.length > 0 ? 'AND' : undefined,
    };
    setLocalConditions([...localConditions, newCondition]);
  };

  // 移除条件
  const removeCondition = (id: string) => {
    const newConditions = localConditions.filter(c => c.id !== id);
    // 移除第一个条件的逻辑操作符
    if (newConditions.length > 0 && newConditions[0].logic) {
      newConditions[0] = { ...newConditions[0], logic: undefined };
    }
    setLocalConditions(newConditions);
  };

  // 更新条件
  const updateCondition = (id: string, updates: Partial<SearchCondition>) => {
    setLocalConditions(localConditions.map(c =>
      c.id === id ? { ...c, ...updates } : c
    ));
  };

  // 执行搜索
  const handleSearch = async () => {
    const validConditions = localConditions.filter(c => {
      if (typeof c.value === 'string') {
        return c.value.trim() !== '';
      }
      if (Array.isArray(c.value)) {
        return c.value.length > 0;
      }
      if (typeof c.value === 'object' && c.value !== null) {
        return c.value.from || c.value.to;
      }
      return false;
    });

    await updateFromAdvancedSearch(validConditions);
    setIsOpen(false);
  };

  // 清空所有条件
  const handleClear = async () => {
    setLocalConditions([]);
    await clearAllConditions();
    setIsOpen(false);
  };

  // 渲染值输入组件
  const renderValueInput = (condition: SearchCondition, field: DatabaseFieldConfig) => {
    const fieldStats = getFieldStatistics(field.fieldName);
    
    switch (field.searchType) {
      case 'date_range':
        const dateValue = typeof condition.value === 'object' && !Array.isArray(condition.value) 
          ? condition.value 
          : { from: '', to: '' };
        return (
          <DateRangePicker
            value={dateValue}
            onChange={(value) => updateCondition(condition.id, { value })}
            placeholder={`Select ${field.displayName.toLowerCase()} range`}
            className="w-48"
          />
        );

      case 'exact':
        if (fieldStats?.items && fieldStats.items.length > 0) {
          // 使用MultiSelect for exact matches with available options
          return (
            <MultiSelect
              options={fieldStats.items.map(item => ({
                value: item.value,
                label: item.value,
                count: item.count
              }))}
              value={Array.isArray(condition.value) ? condition.value : []}
              onValueChange={(value) => updateCondition(condition.id, { value })}
              placeholder={`Select ${field.displayName}`}
              className="w-48"
            />
          );
        }
        // Fallback to input for exact match
        return (
          <Input
            type="text"
            value={typeof condition.value === 'string' ? condition.value : ''}
            onChange={(e) => updateCondition(condition.id, { value: e.target.value })}
            placeholder={`Enter ${field.displayName.toLowerCase()}`}
            className="w-48"
          />
        );

      case 'contains':
      case 'starts_with':
      case 'ends_with':
        return (
          <Input
            type="text"
            value={typeof condition.value === 'string' ? condition.value : ''}
            onChange={(e) => updateCondition(condition.id, { value: e.target.value })}
            placeholder={`Enter ${field.displayName.toLowerCase()}`}
            className="w-48"
          />
        );

      case 'range':
        const rangeValue = typeof condition.value === 'object' && !Array.isArray(condition.value)
          ? condition.value
          : { from: '', to: '' };
        return (
          <div className="flex items-center gap-2">
            <Input
              type="number"
              value={rangeValue.from || ''}
              onChange={(e) => updateCondition(condition.id, { 
                value: { ...rangeValue, from: e.target.value }
              })}
              placeholder="Min"
              className="w-20"
            />
            <span className="text-gray-500">to</span>
            <Input
              type="number"
              value={rangeValue.to || ''}
              onChange={(e) => updateCondition(condition.id, { 
                value: { ...rangeValue, to: e.target.value }
              })}
              placeholder="Max"
              className="w-20"
            />
          </div>
        );

      default:
        return (
          <Input
            type="text"
            value={typeof condition.value === 'string' ? condition.value : ''}
            onChange={(e) => updateCondition(condition.id, { value: e.target.value })}
            placeholder={`Enter ${field.displayName.toLowerCase()}`}
            className="w-48"
          />
        );
    }
  };

  if (!config) {
    return (
      <Button disabled className={className}>
        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
        Loading...
      </Button>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleDialogOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className={className}>
          <Search className="h-4 w-4 mr-2" />
          Advanced Search
          {searchState.conditions.length > 0 && (
            <Badge variant="secondary" className="ml-2">
              {searchState.conditions.length}
            </Badge>
          )}
        </Button>
      </DialogTrigger>
      
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Advanced Search - {database.toUpperCase()}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* 搜索条件列表 */}
          <div className="space-y-3">
            {localConditions.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No search conditions added yet</p>
                <p className="text-sm">Click "Add Condition" to start building your search</p>
              </div>
            ) : (
              localConditions.map((condition, index) => {
                const field = advancedSearchableFields.find(f => f.fieldName === condition.field);
                if (!field) return null;

                return (
                  <div key={condition.id} className="flex items-center gap-3 p-3 border rounded-lg bg-gray-50">
                    {/* 逻辑操作符 */}
                    {index > 0 && (
                      <Select
                        value={condition.logic || 'AND'}
                        onValueChange={(value) => updateCondition(condition.id, { logic: value as 'AND' | 'OR' | 'NOT' })}
                      >
                        <SelectTrigger className="w-20">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {LOGIC_OPERATORS.map(op => (
                            <SelectItem key={op.value} value={op.value}>
                              {op.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}

                    {/* 字段选择 */}
                    <Select
                      value={condition.field}
                      onValueChange={(value) => updateCondition(condition.id, { field: value, value: '' })}
                    >
                      <SelectTrigger className="w-48">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {advancedSearchableFields.map(field => (
                          <SelectItem key={field.fieldName} value={field.fieldName}>
                            {field.displayName}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    {/* 值输入 */}
                    {renderValueInput(condition, field)}

                    {/* 删除按钮 */}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeCondition(condition.id)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                );
              })
            )}
          </div>

          {/* 操作按钮 */}
          <div className="flex items-center justify-between pt-4 border-t">
            <Button
              variant="outline"
              onClick={addCondition}
              disabled={advancedSearchableFields.length === 0}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Condition
            </Button>

            <div className="flex gap-2">
              <Button variant="ghost" onClick={handleClear}>
                Clear All
              </Button>
              <Button 
                onClick={handleSearch}
                disabled={searchState.isLoading || localConditions.length === 0}
              >
                {searchState.isLoading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Search className="h-4 w-4 mr-2" />
                )}
                Search ({localConditions.length})
              </Button>
            </div>
          </div>

          {/* 统计信息 */}
          {searchState.statisticsCache.size > 0 && (
            <div className="text-xs text-gray-600 pt-2 border-t">
              Statistics available for {searchState.statisticsCache.size} fields
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
