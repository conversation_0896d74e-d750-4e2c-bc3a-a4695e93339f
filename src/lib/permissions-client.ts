/**
 * 客户端权限检查
 * 不包含服务器端数据库连接，避免导入问题
 */

// 用户权限级别
export type UserPermissionLevel = 'free' | 'premium' | 'admin';

// 数据库访问级别
export type DatabaseAccessLevel = 'free' | 'premium' | 'admin';

// 数据库配置接口（客户端版本）
export interface DatabaseConfigClient {
  code: string;
  name: string;
  category: string;
  description?: string;
  accessLevel: DatabaseAccessLevel;
  exportConfig?: any;
  sortOrder: number;
}

// 客户端版本不使用 Redis，只使用内存缓存

// 内存缓存
let databaseConfigsCache: DatabaseConfigClient[] | null = null;
let configsCacheExpiry = 0;
const CACHE_TTL = 300000; // 5分钟

/**
 * 检查用户是否可以访问指定数据库（同步版本）
 * 基于缓存的配置进行检查，不访问数据库
 */
export function canAccessDatabaseSync(
  databaseCode: string, 
  userPermissionLevel: UserPermissionLevel = 'free'
): boolean {
  // 如果没有缓存配置，默认允许访问（避免阻塞）
  if (!databaseConfigsCache) {
    console.warn(`[权限检查] 配置缓存未加载，默认允许访问 ${databaseCode}`);
    return true;
  }

  const config = databaseConfigsCache.find(c => c.code === databaseCode);
  if (!config) {
    console.warn(`[权限检查] 未找到数据库配置: ${databaseCode}`);
    return false;
  }

  return checkPermissionLevel(userPermissionLevel, config.accessLevel);
}

/**
 * 检查权限级别是否满足要求
 */
function checkPermissionLevel(
  userLevel: UserPermissionLevel, 
  requiredLevel: DatabaseAccessLevel
): boolean {
  const levelHierarchy: Record<string, number> = {
    'free': 1,
    'premium': 2,
    'admin': 3
  };

  const userLevelValue = levelHierarchy[userLevel] || 0;
  const requiredLevelValue = levelHierarchy[requiredLevel] || 0;

  return userLevelValue >= requiredLevelValue;
}

/**
 * 获取数据库配置（客户端版本）
 * 优先从缓存获取，避免服务器端调用
 */
export function getDatabaseConfigs(): DatabaseConfigClient[] {
  if (databaseConfigsCache && Date.now() < configsCacheExpiry) {
    return databaseConfigsCache;
  }

  // 如果缓存过期或不存在，返回空数组
  // 客户端应该通过 API 调用获取最新配置
  console.warn('[权限检查] 配置缓存已过期，请通过 API 刷新配置');
  return [];
}

/**
 * 设置数据库配置缓存（由 API 调用或预加载器设置）
 */
export function setDatabaseConfigsCache(configs: DatabaseConfigClient[]): void {
  databaseConfigsCache = configs;
  configsCacheExpiry = Date.now() + CACHE_TTL;
  
  console.log(`[权限检查] 配置缓存已更新，包含 ${configs.length} 个数据库`);
}

/**
 * 清除配置缓存
 */
export function clearDatabaseConfigsCache(): void {
  databaseConfigsCache = null;
  configsCacheExpiry = 0;
  
  console.log('[权限检查] 配置缓存已清除');
}

/**
 * 根据用户权限级别过滤数据库配置
 */
export function filterDatabasesByPermission(
  configs: DatabaseConfigClient[], 
  userPermissionLevel: UserPermissionLevel = 'free'
): DatabaseConfigClient[] {
  return configs.filter(config => 
    checkPermissionLevel(userPermissionLevel, config.accessLevel)
  );
}

/**
 * 获取用户可访问的数据库代码列表
 */
export function getAccessibleDatabaseCodes(
  userPermissionLevel: UserPermissionLevel = 'free'
): string[] {
  const configs = getDatabaseConfigs();
  const accessibleConfigs = filterDatabasesByPermission(configs, userPermissionLevel);
  return accessibleConfigs.map(config => config.code);
}

/**
 * 检查是否为高级功能
 */
export function isPremiumFeature(requiredLevel: DatabaseAccessLevel = 'premium'): boolean {
  return requiredLevel === 'premium' || requiredLevel === 'admin';
}

/**
 * 获取权限级别显示名称
 */
export function getPermissionLevelDisplayName(level: UserPermissionLevel): string {
  const displayNames: Record<UserPermissionLevel, string> = {
    'free': '免费用户',
    'premium': '高级用户',
    'admin': '管理员'
  };
  
  return displayNames[level] || '未知';
}

/**
 * 获取数据库访问级别显示名称
 */
export function getAccessLevelDisplayName(level: DatabaseAccessLevel): string {
  const displayNames: Record<DatabaseAccessLevel, string> = {
    'free': '免费',
    'premium': '高级',
    'admin': '管理员'
  };
  
  return displayNames[level] || '未知';
}


// 兼容性导出 - 从原始 permissions.ts 迁移
export const DATABASE_CONFIGS: DatabaseConfigClient[] = [];

export type MembershipType = UserPermissionLevel;

export const MEMBERSHIP_BENEFITS = {
  free: {
    name: '免费版',
    databases: ['us_class'],
    features: ['基础搜索', '基础筛选']
  },
  premium: {
    name: '高级版',
    databases: ['us_pmn', 'us_class', 'deviceCN', 'deviceHK'],
    features: ['高级搜索', '数据导出', '详细信息']
  },
  admin: {
    name: '管理员',
    databases: ['*'],
    features: ['所有功能', '用户管理', '系统配置']
  }
};

// 兼容性函数
export function getDatabaseAccessLevel(databaseCode: string): DatabaseAccessLevel {
  const configs = getDatabaseConfigs();
  const config = configs.find(c => c.code === databaseCode);
  return config?.accessLevel || 'free';
}
