import Redis from 'ioredis';
import { db } from './db-server';
import { databaseConfigs, fieldConfigs } from '../db/schema';
import { eq } from 'drizzle-orm';

// Redis客户端配置
const redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6379');

// 缓存键前缀
const CACHE_PREFIX = 'drizzle_db_config:';
const FIELD_CONFIG_KEY = (databaseCode: string) => `${CACHE_PREFIX}fields:${databaseCode}`;
const ALL_CONFIG_KEY = (databaseCode: string) => `${CACHE_PREFIX}all:${databaseCode}`;

// 缓存TTL（5分钟）
const CACHE_TTL = 300;

// 数据库字段配置接口
export interface DatabaseFieldConfig {
  id: string;
  databaseCode: string;
  fieldName: string;
  displayName: string;
  fieldType: 'text' | 'date' | 'number' | 'boolean' | 'select' | 'json';
  isVisible: boolean;
  isSearchable: boolean;
  isFilterable: boolean;
  isAdvancedSearchable: boolean;
  isSortable: boolean;
  sortOrder: number;
  listOrder: number;
  detailOrder: number;
  searchType: 'exact' | 'contains' | 'range' | 'date_range' | 'starts_with' | 'ends_with';
  filterType: 'select' | 'input' | 'date_range' | 'checkbox' | 'multi_select' | 'range';
  validationRules?: any;
  options?: any;
  todetail: boolean;
  isStatisticsEnabled: boolean;
  statisticsOrder: number;
  statisticsType: 'count' | 'sum' | 'avg' | 'min_max' | 'group_by';
  statisticsDisplayName?: string;
  statisticsConfig?: any;
  statisticsSortOrder?: string;
  statisticsDefaultLimit: number;
  statisticsMaxLimit: number;
  isExportable: boolean;
  exportOrder: number;
  exportDisplayName?: string;
  filterOrder: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// 数据库排序配置接口
export interface DatabaseSortConfig {
  field: string;
  order: 'asc' | 'desc';
}

// 数据库配置接口
export interface DatabaseConfig {
  fields: DatabaseFieldConfig[];
  defaultSort?: DatabaseSortConfig[];
}

/**
 * Drizzle 配置缓存服务
 * 替代原有的 Prisma 配置缓存
 */
export class DrizzleConfigCacheService {
  
  /**
   * 获取字段配置
   * @param databaseCode 数据库代码
   * @returns 字段配置数组
   */
  static async getFieldConfigs(databaseCode: string): Promise<DatabaseFieldConfig[]> {
    const cacheKey = FIELD_CONFIG_KEY(databaseCode);
    
    try {
      // 尝试从缓存获取
      const cached = await redis.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }
    } catch (error) {
      console.warn('Redis缓存读取失败，从数据库获取字段配置:', error);
    }

    // 从数据库获取字段配置
    const configs = await db
      .select()
      .from(fieldConfigs)
      .where(eq(fieldConfigs.databaseCode, databaseCode));

    // 转换为接口格式
    const fieldConfigsData: DatabaseFieldConfig[] = configs.map(config => ({
      id: config.id,
      databaseCode: config.databaseCode,
      fieldName: config.fieldName,
      displayName: config.displayName,
      fieldType: config.fieldType,
      isVisible: config.isVisible,
      isSearchable: config.isSearchable,
      isFilterable: config.isFilterable,
      isAdvancedSearchable: config.isAdvancedSearchable,
      isSortable: config.isSortable,
      sortOrder: config.sortOrder,
      listOrder: config.listOrder,
      detailOrder: config.detailOrder,
      searchType: config.searchType,
      filterType: config.filterType,
      validationRules: config.validationRules,
      options: config.options,
      todetail: config.todetail,
      isStatisticsEnabled: config.isStatisticsEnabled,
      statisticsOrder: config.statisticsOrder,
      statisticsType: config.statisticsType,
      statisticsDisplayName: config.statisticsDisplayName,
      statisticsConfig: config.statisticsConfig,
      statisticsSortOrder: config.statisticsSortOrder,
      statisticsDefaultLimit: config.statisticsDefaultLimit,
      statisticsMaxLimit: config.statisticsMaxLimit,
      isExportable: config.isExportable,
      exportOrder: config.exportOrder,
      exportDisplayName: config.exportDisplayName,
      filterOrder: config.filterOrder,
      isActive: config.isActive,
      createdAt: config.createdAt,
      updatedAt: config.updatedAt,
    }));

    // 缓存结果
    try {
      await redis.setex(cacheKey, CACHE_TTL, JSON.stringify(fieldConfigsData));
    } catch (error) {
      console.warn('Redis缓存写入失败:', error);
    }

    return fieldConfigsData;
  }

  /**
   * 获取完整数据库配置
   * @param databaseCode 数据库代码
   * @returns 数据库配置
   */
  static async getDatabaseConfig(databaseCode: string): Promise<DatabaseConfig> {
    const cacheKey = ALL_CONFIG_KEY(databaseCode);
    
    try {
      // 尝试从缓存获取
      const cached = await redis.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }
    } catch (error) {
      console.warn('Redis缓存读取失败，从数据库获取配置:', error);
    }

    // 获取字段配置
    const fields = await this.getFieldConfigs(databaseCode);

    // 获取数据库配置（只获取默认排序）
    const dbConfigResult = await db
      .select({
        defaultSort: databaseConfigs.defaultSort
      })
      .from(databaseConfigs)
      .where(eq(databaseConfigs.code, databaseCode))
      .limit(1);

    // 解析默认排序配置
    let defaultSort: DatabaseSortConfig[] | undefined;
    if (dbConfigResult.length > 0 && dbConfigResult[0].defaultSort) {
      try {
        defaultSort = dbConfigResult[0].defaultSort as unknown as DatabaseSortConfig[];
      } catch (error) {
        console.warn(`解析 ${databaseCode} 默认排序配置失败:`, error);
      }
    }

    const config = {
      fields,
      defaultSort,
    };

    // 缓存结果
    try {
      await redis.setex(cacheKey, CACHE_TTL, JSON.stringify(config));
    } catch (error) {
      console.warn('Redis缓存写入失败:', error);
    }

    return config;
  }

  /**
   * 清除数据库配置缓存
   * @param databaseCode 数据库代码
   */
  static async clearDatabaseCache(databaseCode: string): Promise<void> {
    const keys = [
      FIELD_CONFIG_KEY(databaseCode),
      ALL_CONFIG_KEY(databaseCode),
    ];

    try {
      await redis.del(...keys);
    } catch (error) {
      console.warn('Redis缓存清除失败:', error);
    }
  }

  /**
   * 清除所有缓存
   */
  static async clearAllCache(): Promise<void> {
    try {
      const keys = await redis.keys(`${CACHE_PREFIX}*`);
      if (keys.length > 0) {
        await redis.del(...keys);
      }
    } catch (error) {
      console.warn('Redis缓存清除失败:', error);
    }
  }
}

// 向后兼容的导出函数
export async function getDatabaseConfig(databaseCode: string): Promise<DatabaseConfig> {
  return DrizzleConfigCacheService.getDatabaseConfig(databaseCode);
}

export async function getFieldConfigs(databaseCode: string): Promise<DatabaseFieldConfig[]> {
  return DrizzleConfigCacheService.getFieldConfigs(databaseCode);
}

export async function clearDatabaseCache(databaseCode: string): Promise<void> {
  return DrizzleConfigCacheService.clearDatabaseCache(databaseCode);
}

// 验证数据库配置
export function validateDatabaseConfig(config: DatabaseConfig): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!config.fields || !Array.isArray(config.fields)) {
    errors.push('字段配置必须是数组');
  }

  if (config.fields && config.fields.length === 0) {
    errors.push('至少需要一个字段配置');
  }

  // 检查必需字段
  config.fields?.forEach((field, index) => {
    if (!field.fieldName) {
      errors.push(`字段 ${index + 1} 缺少 fieldName`);
    }
    if (!field.displayName) {
      errors.push(`字段 ${index + 1} 缺少 displayName`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
}
