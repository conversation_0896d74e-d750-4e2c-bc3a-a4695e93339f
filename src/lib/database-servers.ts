/**
 * 多数据库服务器配置管理
 * 支持不同数据库连接到不同的服务器IP地址
 */

import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from '../db/schema';

// 数据库服务器配置接口
export interface DatabaseServerConfig {
  id: string;
  name: string;
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  ssl?: boolean;
  maxConnections?: number;
  idleTimeout?: number;
  connectTimeout?: number;
  isActive: boolean;
  description?: string;
}

// 数据库到服务器的映射配置
export interface DatabaseMapping {
  databaseCode: string;
  serverId: string;
  tableName: string;
  isActive: boolean;
}

// 连接池管理
class DatabaseConnectionManager {
  private connections = new Map<string, postgres.Sql>();
  private drizzleInstances = new Map<string, ReturnType<typeof drizzle>>();

  /**
   * 获取数据库连接
   */
  getConnection(serverId: string, config: DatabaseServerConfig): postgres.Sql {
    if (this.connections.has(serverId)) {
      return this.connections.get(serverId)!;
    }

    const connectionString = `postgresql://${config.username}:${config.password}@${config.host}:${config.port}/${config.database}${config.ssl ? '?sslmode=require' : ''}`;
    
    const connection = postgres(connectionString, {
      max: config.maxConnections || 20,
      idle_timeout: config.idleTimeout || 20,
      connect_timeout: config.connectTimeout || 10,
    });

    this.connections.set(serverId, connection);
    return connection;
  }

  /**
   * 获取Drizzle实例
   */
  getDrizzleInstance(serverId: string, config: DatabaseServerConfig) {
    if (this.drizzleInstances.has(serverId)) {
      return this.drizzleInstances.get(serverId)!;
    }

    const connection = this.getConnection(serverId, config);
    const db = drizzle(connection, { schema });
    
    this.drizzleInstances.set(serverId, db);
    return db;
  }

  /**
   * 关闭连接
   */
  async closeConnection(serverId: string) {
    const connection = this.connections.get(serverId);
    if (connection) {
      await connection.end();
      this.connections.delete(serverId);
      this.drizzleInstances.delete(serverId);
    }
  }

  /**
   * 关闭所有连接
   */
  async closeAllConnections() {
    const promises = Array.from(this.connections.keys()).map(serverId => 
      this.closeConnection(serverId)
    );
    await Promise.all(promises);
  }
}

// 全局连接管理器实例
export const connectionManager = new DatabaseConnectionManager();

// 默认服务器配置（从环境变量读取）
export const getDefaultServerConfig = (): DatabaseServerConfig => {
  const databaseUrl = process.env.DATABASE_URL;
  if (!databaseUrl) {
    throw new Error('DATABASE_URL environment variable is not set');
  }

  // 解析DATABASE_URL
  const url = new URL(databaseUrl);
  
  return {
    id: 'default',
    name: 'Default Database Server',
    host: url.hostname,
    port: parseInt(url.port) || 5432,
    database: url.pathname.slice(1), // 移除开头的 '/'
    username: url.username,
    password: url.password,
    ssl: url.searchParams.get('sslmode') === 'require',
    maxConnections: 20,
    idleTimeout: 20,
    connectTimeout: 10,
    isActive: true,
    description: 'Default PostgreSQL server from DATABASE_URL'
  };
};

// 服务器配置存储（将来可以从数据库或配置文件读取）
class DatabaseServerConfigStore {
  private servers = new Map<string, DatabaseServerConfig>();
  private mappings = new Map<string, DatabaseMapping>();

  constructor() {
    // 初始化默认服务器
    const defaultConfig = getDefaultServerConfig();
    this.servers.set('default', defaultConfig);
  }

  /**
   * 添加服务器配置
   */
  addServer(config: DatabaseServerConfig) {
    this.servers.set(config.id, config);
  }

  /**
   * 获取服务器配置
   */
  getServer(serverId: string): DatabaseServerConfig | undefined {
    return this.servers.get(serverId);
  }

  /**
   * 获取所有活跃的服务器
   */
  getActiveServers(): DatabaseServerConfig[] {
    return Array.from(this.servers.values()).filter(server => server.isActive);
  }

  /**
   * 添加数据库映射
   */
  addMapping(mapping: DatabaseMapping) {
    this.mappings.set(mapping.databaseCode, mapping);
  }

  /**
   * 获取数据库映射
   */
  getMapping(databaseCode: string): DatabaseMapping | undefined {
    return this.mappings.get(databaseCode);
  }

  /**
   * 获取数据库对应的服务器配置
   */
  getServerForDatabase(databaseCode: string): DatabaseServerConfig | undefined {
    const mapping = this.getMapping(databaseCode);
    if (!mapping) {
      // 如果没有特定映射，使用默认服务器
      return this.getServer('default');
    }
    return this.getServer(mapping.serverId);
  }
}

// 全局配置存储实例
export const serverConfigStore = new DatabaseServerConfigStore();

/**
 * 获取指定数据库的Drizzle实例
 */
export function getDatabaseInstance(databaseCode: string) {
  const serverConfig = serverConfigStore.getServerForDatabase(databaseCode);
  if (!serverConfig) {
    throw new Error(`No server configuration found for database: ${databaseCode}`);
  }

  return connectionManager.getDrizzleInstance(serverConfig.id, serverConfig);
}

/**
 * 从配置文件加载服务器配置
 */
async function loadServerConfigFromFile() {
  try {
    // 在服务器端加载配置文件
    if (typeof window === 'undefined') {
      const fs = await import('fs');
      const path = await import('path');

      const configPath = path.join(process.cwd(), 'src/config/database-servers.json');

      if (fs.existsSync(configPath)) {
        const configData = fs.readFileSync(configPath, 'utf-8');
        const config = JSON.parse(configData);

        // 加载服务器配置
        config.servers?.forEach((server: DatabaseServerConfig) => {
          // 如果配置中没有密码，尝试从环境变量获取
          if (!server.password && process.env[`DB_PASSWORD_${server.id.toUpperCase()}`]) {
            server.password = process.env[`DB_PASSWORD_${server.id.toUpperCase()}`]!;
          }

          serverConfigStore.addServer(server);
        });

        // 加载数据库映射
        config.mappings?.forEach((mapping: DatabaseMapping) => {
          serverConfigStore.addMapping(mapping);
        });

        console.log(`✅ 已加载 ${config.servers?.length || 0} 个服务器配置和 ${config.mappings?.length || 0} 个数据库映射`);
      }
    }
  } catch (error) {
    console.warn('⚠️ 加载服务器配置文件失败，使用默认配置:', error);
  }
}

/**
 * 初始化数据库服务器配置
 */
export async function initializeDatabaseServers() {
  // 从配置文件加载
  await loadServerConfigFromFile();

  // 添加默认映射（如果配置文件中没有）
  if (!serverConfigStore.getMapping('us_pmn')) {
    serverConfigStore.addMapping({
      databaseCode: 'us_pmn',
      serverId: 'default',
      tableName: 'us_pmn',
      isActive: true
    });
  }

  if (!serverConfigStore.getMapping('us_class')) {
    serverConfigStore.addMapping({
      databaseCode: 'us_class',
      serverId: 'default',
      tableName: 'us_class',
      isActive: true
    });
  }
}

// 延迟初始化，避免在模块加载时执行
let initialized = false;

export async function ensureInitialized() {
  if (!initialized && typeof window === 'undefined') {
    await initializeDatabaseServers();
    initialized = true;
  }
}
