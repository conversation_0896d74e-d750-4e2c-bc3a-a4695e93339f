import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from '../db/schema';

// 创建 PostgreSQL 连接
const connectionString = process.env.DATABASE_URL!;

// 在全局对象上声明一个 postgres 连接实例
declare global {
  var __drizzleConnection: postgres.Sql | undefined;
}

// 在开发环境中，使用全局对象来缓存连接实例，
// 避免在 Next.js 的热重载中创建过多的数据库连接。
// 在生产环境中，则直接创建一个新的实例。
const connection = globalThis.__drizzleConnection || postgres(connectionString, {
  max: 20,
  idle_timeout: 20,
  connect_timeout: 10,
});

if (process.env.NODE_ENV !== 'production') {
  globalThis.__drizzleConnection = connection;
}

// 创建 Drizzle 实例
export const db = drizzle(connection, { schema });

// 导出类型
export type Database = typeof db;
export * from '../db/schema';
