/**
 * 可靠的数据库配置加载器
 * 解决首次加载时数据不显示的问题
 */

import { getDatabaseConfigs } from "@/lib/permissions-client";

// 全局状态管理
let globalConfigs: Record<string, any> | null = null;
let loadingPromise: Promise<Record<string, any>> | null = null;
let loadAttempts = 0;
const MAX_ATTEMPTS = 3;

// 监听器列表
const listeners: Array<(configs: Record<string, any>) => void> = [];

/**
 * 添加配置更新监听器
 */
export function addConfigListener(callback: (configs: Record<string, any>) => void) {
  listeners.push(callback);
  
  // 如果已经有配置，立即调用回调
  if (globalConfigs) {
    callback(globalConfigs);
  }
  
  // 返回取消监听的函数
  return () => {
    const index = listeners.indexOf(callback);
    if (index > -1) {
      listeners.splice(index, 1);
    }
  };
}

/**
 * 通知所有监听器
 */
function notifyListeners(configs: Record<string, any>) {
  listeners.forEach(callback => {
    try {
      callback(configs);
    } catch (error) {
      console.error('Config listener error:', error);
    }
  });
}

/**
 * 可靠的配置加载函数
 */
export async function loadDatabaseConfigs(forceReload = false): Promise<Record<string, any>> {
  // 如果已经有配置且不强制重新加载，直接返回
  if (globalConfigs && !forceReload) {
    return globalConfigs;
  }
  
  // 如果正在加载中，等待现有的加载完成
  if (loadingPromise && !forceReload) {
    return loadingPromise;
  }
  
  loadingPromise = performLoad();
  return loadingPromise;
}

/**
 * 执行实际的加载操作
 */
async function performLoad(): Promise<Record<string, any>> {
  loadAttempts++;
  console.log(`[ConfigLoader] 开始加载配置 (尝试 ${loadAttempts}/${MAX_ATTEMPTS})`);
  
  try {
    const configs = await getDatabaseConfigs();
    console.log(`[ConfigLoader] 成功加载 ${Object.keys(configs).length} 个数据库配置`);
    
    globalConfigs = configs;
    loadingPromise = null;
    loadAttempts = 0;
    
    // 通知所有监听器
    notifyListeners(configs);
    
    return configs;
    
  } catch (error) {
    console.error(`[ConfigLoader] 加载失败 (尝试 ${loadAttempts}/${MAX_ATTEMPTS}):`, error);
    
    if (loadAttempts < MAX_ATTEMPTS) {
      // 重试，使用指数退避
      const delay = Math.pow(2, loadAttempts - 1) * 1000;
      console.log(`[ConfigLoader] ${delay}ms 后重试...`);
      
      await new Promise(resolve => setTimeout(resolve, delay));
      return performLoad();
    } else {
      // 达到最大重试次数，重置状态
      loadingPromise = null;
      loadAttempts = 0;
      
      // 返回空配置，但不设置为全局配置
      const emptyConfigs = {};
      notifyListeners(emptyConfigs);
      return emptyConfigs;
    }
  }
}

/**
 * 获取当前配置（同步）
 */
export function getCurrentConfigs(): Record<string, any> | null {
  return globalConfigs;
}

/**
 * 检查是否正在加载
 */
export function isLoading(): boolean {
  return loadingPromise !== null;
}

/**
 * 清除配置缓存
 */
export function clearConfigCache(): void {
  globalConfigs = null;
  loadingPromise = null;
  loadAttempts = 0;
}

/**
 * 预加载配置
 */
export function preloadConfigs(): void {
  if (!globalConfigs && !loadingPromise) {
    console.log('[ConfigLoader] 开始预加载配置...');
    loadDatabaseConfigs().catch(error => {
      console.warn('[ConfigLoader] 预加载失败:', error);
    });
  }
}

// 在浏览器环境中自动预加载
if (typeof window !== 'undefined') {
  // 立即开始预加载
  preloadConfigs();
  
  // 监听页面可见性变化，在页面重新可见时刷新配置
  document.addEventListener('visibilitychange', () => {
    if (!document.hidden && !globalConfigs) {
      console.log('[ConfigLoader] 页面重新可见，尝试加载配置...');
      preloadConfigs();
    }
  });
}
