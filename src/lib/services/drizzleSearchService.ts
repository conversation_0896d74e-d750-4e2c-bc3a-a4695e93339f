/**
 * 完全基于Drizzle的统一搜索服务
 * 替代原有的Prisma搜索逻辑
 */

import { and, or, ilike, gte, lte, inArray, count, desc, asc, eq, ne, isNull, not, SQL } from 'drizzle-orm';
import { db } from '../db-server';
import { getDynamicTable, isDrizzleTable } from '../drizzleTableMapping';
import { getDatabaseConfig } from '../configCache';
// import { buildAdvancedSearchWhere } from '../server/buildDrizzleWhere';

// 搜索条件接口
export interface SearchCondition {
  id: string;
  field: string;
  value: string | string[] | { from?: string; to?: string };
  logic?: 'AND' | 'OR' | 'NOT';
}

// 搜索参数接口
export interface DrizzleSearchParams {
  database: string;
  conditions?: SearchCondition[];
  globalKeyword?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 搜索结果接口
export interface DrizzleSearchResult {
  success: boolean;
  data: any[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  searchInfo: {
    database: string;
    conditions: SearchCondition[];
    globalKeyword?: string;
    searchTime: number;
    esSearchTime?: number;
  };
  error?: string;
}

// 字段统计结果接口
export interface FieldStatistics {
  field: string;
  items: Array<{
    value: string;
    count: number;
  }>;
  total: number;
}

/**
 * Drizzle统一搜索服务
 */
export class DrizzleSearchService {
  
  /**
   * 执行统一搜索
   */
  static async search(params: DrizzleSearchParams): Promise<DrizzleSearchResult> {
    const startTime = performance.now();
    const {
      database,
      conditions = [],
      globalKeyword,
      page = 1,
      limit = 20,
      sortBy,
      sortOrder = 'desc'
    } = params;

    try {
      console.log('[DrizzleSearchService] 开始搜索:', {
        database,
        conditions: conditions.length,
        globalKeyword: globalKeyword ? '有' : '无',
        page,
        limit,
        sortBy,
        sortOrder
      });

      // 获取数据库配置和表对象
      const config = await getDatabaseConfig(database);
      const table = await getDynamicTable(database);

      if (!isDrizzleTable(table)) {
        throw new Error(`无效的数据库表: ${database}`);
      }

      // 获取可见字段
      const visibleFields = config.fields.filter(f => f.isVisible);
      const sortableFields = config.fields.filter(f => f.isSortable);

      // 构建WHERE条件
      const whereConditions = this.buildWhereConditions(table, conditions, globalKeyword, config);

      // 构建排序
      const orderBy = this.buildOrderBy(table, sortBy, sortOrder, sortableFields, config);

      // 执行查询
      const offset = (page - 1) * limit;

      // 构建基础查询
      let baseQuery = db.select().from(table);

      if (whereConditions) {
        baseQuery = baseQuery.where(whereConditions);
      }

      if (orderBy.length > 0) {
        baseQuery = baseQuery.orderBy(...orderBy);
      }

      const data = await baseQuery.limit(limit).offset(offset);

      // 查询总数
      let countQuery = db.select({ count: count() }).from(table);
      if (whereConditions) {
        countQuery = countQuery.where(whereConditions);
      }
      const [{ count: total }] = await countQuery;

      // 过滤返回的字段，只返回可见字段
      const filteredData = data.map(item => {
        const filtered: Record<string, any> = { id: item.id };
        visibleFields.forEach(fieldConfig => {
          if (item.hasOwnProperty(fieldConfig.fieldName)) {
            filtered[fieldConfig.fieldName] = item[fieldConfig.fieldName];
          }
        });
        return filtered;
      });

      const searchTime = performance.now() - startTime;

      console.log('[DrizzleSearchService] 搜索完成:', {
        dataCount: filteredData.length,
        total,
        searchTime: Math.round(searchTime)
      });

      return {
        success: true,
        data: filteredData,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        },
        searchInfo: {
          database,
          conditions,
          globalKeyword,
          searchTime
        }
      };

    } catch (error) {
      console.error('[DrizzleSearchService] 搜索失败:', error);

      return {
        success: false,
        data: [],
        pagination: { page, limit, total: 0, totalPages: 0 },
        searchInfo: {
          database,
          conditions,
          globalKeyword,
          searchTime: performance.now() - startTime
        },
        error: error instanceof Error ? error.message : '搜索失败'
      };
    }
  }

  /**
   * 获取字段统计信息
   */
  static async getFieldStatistics(
    database: string,
    fieldName: string,
    conditions: SearchCondition[] = [],
    limit: number = 50
  ): Promise<FieldStatistics> {
    try {
      console.log('[DrizzleSearchService] 获取字段统计:', {
        database,
        fieldName,
        conditions: conditions.length,
        limit
      });

      // 获取数据库配置和表对象
      const config = await getDatabaseConfig(database);
      const table = await getDynamicTable(database);

      if (!isDrizzleTable(table) || !table[fieldName]) {
        throw new Error(`字段 ${fieldName} 在表 ${database} 中不存在`);
      }

      const field = table[fieldName];
      const whereConditions = this.buildWhereConditions(table, conditions, undefined, config);

      // 构建统计查询 - 排除空值
      const query = db
        .select({
          value: field,
          count: count()
        })
        .from(table)
        .where(
          and(
            whereConditions || undefined,
            ne(field, ''),
            not(isNull(field))
          )
        )
        .groupBy(field)
        .orderBy(desc(count()))
        .limit(limit);

      const results = await query;

      // 计算总数
      let totalQuery = db.select({ count: count() }).from(table);
      if (whereConditions) {
        totalQuery = totalQuery.where(whereConditions);
      }
      const [{ count: total }] = await totalQuery;

      return {
        field: fieldName,
        items: results.map(r => ({
          value: String(r.value || ''),
          count: r.count
        })),
        total
      };

    } catch (error) {
      console.error('[DrizzleSearchService] 获取字段统计失败:', error);
      return {
        field: fieldName,
        items: [],
        total: 0
      };
    }
  }

  /**
   * 构建WHERE条件
   */
  private static buildWhereConditions(
    table: any,
    conditions: SearchCondition[],
    globalKeyword?: string,
    config?: any
  ): SQL | undefined {
    const whereConditions: SQL[] = [];

    // 处理全局关键词搜索
    if (globalKeyword && globalKeyword.trim() && config) {
      const keyword = globalKeyword.trim();
      const searchableFields = config.fields.filter((f: any) => f.isSearchable && f.searchType === 'contains');

      if (searchableFields.length > 0) {
        const globalSearchConditions = searchableFields
          .map((f: any) => {
            const column = table[f.fieldName];
            return column ? ilike(column, `%${keyword}%`) : null;
          })
          .filter(Boolean) as SQL[];

        if (globalSearchConditions.length > 0) {
          whereConditions.push(or(...globalSearchConditions)!);
        }
      }
    }

    // 处理搜索条件
    conditions.forEach(condition => {
      const field = table[condition.field];
      if (!field) return;

      const { value } = condition;

      if (typeof value === 'string') {
        if (value.trim()) {
          whereConditions.push(ilike(field, `%${value.trim()}%`));
        }
      } else if (Array.isArray(value)) {
        if (value.length > 0) {
          // 检查是否包含N/A值
          const hasNullValue = value.includes('N/A');
          if (hasNullValue) {
            const nonNullValues = value.filter(v => v !== 'N/A');
            if (nonNullValues.length > 0) {
              whereConditions.push(
                or(
                  inArray(field, nonNullValues),
                  isNull(field),
                  eq(field, '')
                )!
              );
            } else {
              whereConditions.push(
                or(
                  isNull(field),
                  eq(field, '')
                )!
              );
            }
          } else {
            whereConditions.push(inArray(field, value));
          }
        }
      } else if (typeof value === 'object' && value !== null) {
        const { from, to } = value;
        if (from) {
          whereConditions.push(gte(field, from));
        }
        if (to) {
          whereConditions.push(lte(field, to));
        }
      }
    });

    // 组合所有条件
    if (whereConditions.length === 0) {
      return undefined;
    } else if (whereConditions.length === 1) {
      return whereConditions[0];
    } else {
      return and(...whereConditions);
    }
  }

  /**
   * 构建排序
   */
  private static buildOrderBy(
    table: any,
    sortBy?: string,
    sortOrder: 'asc' | 'desc' = 'desc',
    sortableFields?: any[],
    config?: any
  ) {
    const orderByClause = [];

    // 检查排序字段是否有效
    if (sortBy && table[sortBy] && sortableFields?.some(f => f.fieldName === sortBy)) {
      const field = table[sortBy];
      orderByClause.push(sortOrder === 'asc' ? asc(field) : desc(field));
    } else {
      // 使用默认排序配置
      if (config?.defaultSort && Array.isArray(config.defaultSort)) {
        config.defaultSort.forEach((sort: any) => {
          if (table[sort.field]) {
            const field = table[sort.field];
            orderByClause.push(sort.order === 'asc' ? asc(field) : desc(field));
          }
        });
      }

      // 如果没有默认排序，按ID降序排序
      if (orderByClause.length === 0 && table.id) {
        orderByClause.push(desc(table.id));
      }
    }

    return orderByClause;
  }
}
