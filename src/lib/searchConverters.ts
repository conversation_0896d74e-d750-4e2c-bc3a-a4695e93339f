/**
 * 搜索参数转换器
 * 用于在Filter面板参数和Advanced Search条件之间进行转换
 */

import { SearchCondition } from '@/components/AdvancedSearch';
import { DatabaseConfig } from '@/lib/config/database';

/**
 * 将Filter面板的筛选参数转换为Advanced Search条件
 */
export function convertFiltersToAdvancedConditions(
  filters: Record<string, any>,
  config: DatabaseConfig
): SearchCondition[] {
  const conditions: SearchCondition[] = [];
  let conditionId = 1;

  // 遍历所有筛选参数
  Object.entries(filters).forEach(([key, value]) => {
    // 跳过空值
    if (value === undefined || value === null || value === '' || 
        (Array.isArray(value) && value.length === 0)) {
      return;
    }

    // 处理日期范围字段（以From/To结尾的字段）
    if (key.endsWith('From') || key.endsWith('To')) {
      const baseFieldName = key.replace(/(From|To)$/, '');
      const fieldConfig = config.fields.find(f => f.fieldName === baseFieldName);
      
      if (fieldConfig && fieldConfig.filterType === 'date_range') {
        // 检查是否已经处理过这个日期范围字段
        const existingCondition = conditions.find(c => c.field === baseFieldName);
        
        if (existingCondition) {
          // 更新现有条件的值
          const dateRange = existingCondition.value as { from?: string; to?: string };
          if (key.endsWith('From')) {
            dateRange.from = value as string;
          } else {
            dateRange.to = value as string;
          }
        } else {
          // 创建新的日期范围条件
          const dateRange: { from?: string; to?: string } = {};
          if (key.endsWith('From')) {
            dateRange.from = value as string;
          } else {
            dateRange.to = value as string;
          }
          
          conditions.push({
            id: `condition_${conditionId++}`,
            field: baseFieldName,
            value: dateRange,
            logic: conditions.length > 0 ? 'AND' : undefined
          });
        }
      }
      return;
    }

    // 查找对应的字段配置
    const fieldConfig = config.fields.find(f => f.fieldName === key);
    if (!fieldConfig || !fieldConfig.isAdvancedSearchable) {
      return;
    }

    // 根据字段类型转换值
    let conditionValue: string | string[] | { from?: string; to?: string };

    if (fieldConfig.filterType === 'multi_select' || fieldConfig.filterType === 'checkbox') {
      // 多选字段
      conditionValue = Array.isArray(value) ? value : [value];
    } else if (fieldConfig.filterType === 'select' || fieldConfig.filterType === 'input') {
      // 单选或输入字段
      conditionValue = String(value);
    } else {
      // 默认处理
      conditionValue = String(value);
    }

    conditions.push({
      id: `condition_${conditionId++}`,
      field: key,
      value: conditionValue,
      logic: conditions.length > 0 ? 'AND' : undefined
    });
  });

  return conditions;
}

/**
 * 将Advanced Search条件转换为Filter面板的筛选参数
 */
export function convertAdvancedConditionsToFilters(
  conditions: SearchCondition[],
  config: DatabaseConfig
): Record<string, any> {
  const filters: Record<string, any> = {};

  conditions.forEach(condition => {
    const fieldConfig = config.fields.find(f => f.fieldName === condition.field);
    if (!fieldConfig) {
      return;
    }

    // 处理日期范围
    if (fieldConfig.filterType === 'date_range' && 
        typeof condition.value === 'object' && 
        condition.value !== null && 
        !Array.isArray(condition.value)) {
      
      const dateRange = condition.value as { from?: string; to?: string };
      if (dateRange.from) {
        filters[`${condition.field}From`] = dateRange.from;
      }
      if (dateRange.to) {
        filters[`${condition.field}To`] = dateRange.to;
      }
      return;
    }

    // 处理其他类型的字段
    if (Array.isArray(condition.value)) {
      // 多选字段
      filters[condition.field] = condition.value;
    } else if (typeof condition.value === 'string') {
      // 单选或输入字段
      filters[condition.field] = condition.value;
    }
  });

  return filters;
}

/**
 * 合并Filter参数和Advanced Search条件
 * 用于统一搜索时同时应用两种搜索条件
 */
export function mergeFiltersAndAdvancedConditions(
  filters: Record<string, any>,
  advancedConditions: SearchCondition[],
  config: DatabaseConfig
): SearchCondition[] {
  // 将Filter参数转换为Advanced Search条件
  const filterConditions = convertFiltersToAdvancedConditions(filters, config);
  
  // 合并条件，Advanced Search条件优先
  const mergedConditions: SearchCondition[] = [];
  const processedFields = new Set<string>();

  // 先添加Advanced Search条件
  advancedConditions.forEach(condition => {
    mergedConditions.push(condition);
    processedFields.add(condition.field);
    
    // 对于日期范围字段，也要标记From/To字段为已处理
    const fieldConfig = config.fields.find(f => f.fieldName === condition.field);
    if (fieldConfig && fieldConfig.filterType === 'date_range') {
      processedFields.add(`${condition.field}From`);
      processedFields.add(`${condition.field}To`);
    }
  });

  // 再添加Filter条件（跳过已处理的字段）
  filterConditions.forEach(condition => {
    if (!processedFields.has(condition.field)) {
      // 调整logic，确保第一个条件没有logic
      const adjustedCondition = {
        ...condition,
        logic: mergedConditions.length > 0 ? 'AND' : undefined
      };
      mergedConditions.push(adjustedCondition);
    }
  });

  return mergedConditions;
}

/**
 * 检查两个搜索条件数组是否相等
 */
export function areConditionsEqual(
  conditions1: SearchCondition[],
  conditions2: SearchCondition[]
): boolean {
  if (conditions1.length !== conditions2.length) {
    return false;
  }

  // 按字段名排序后比较
  const sorted1 = [...conditions1].sort((a, b) => a.field.localeCompare(b.field));
  const sorted2 = [...conditions2].sort((a, b) => a.field.localeCompare(b.field));

  return sorted1.every((condition1, index) => {
    const condition2 = sorted2[index];
    return (
      condition1.field === condition2.field &&
      condition1.logic === condition2.logic &&
      JSON.stringify(condition1.value) === JSON.stringify(condition2.value)
    );
  });
}
