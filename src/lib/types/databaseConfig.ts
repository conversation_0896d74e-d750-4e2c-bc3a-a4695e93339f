/**
 * Enhanced Database Configuration Types for Drizzle ORM
 * 
 * This file defines the enhanced type system for database configurations
 * optimized for Drizzle ORM usage patterns.
 */

// ==== Core Configuration Types ====

export type AccessLevel = 'free' | 'premium' | 'enterprise';
export type CacheStrategy = 'redis' | 'memory' | 'none';
export type ConnectionPool = 'default' | 'readonly' | 'analytics' | 'export';
export type ExportFormat = 'csv' | 'xlsx' | 'json' | 'xml';

// ==== Index Strategy Configuration ====
export interface IndexStrategy {
  /** Primary search fields that should have individual indexes */
  searchIndexes?: string[];
  /** Composite indexes for common query patterns */
  compositeIndexes?: Array<{
    fields: string[];
    name: string;
    unique?: boolean;
  }>;
  /** Full-text search configuration */
  fullTextSearch?: {
    fields: string[];
    language?: string;
  };
}

// ==== Export Configuration ====
export interface ExportConfiguration {
  /** Supported export formats */
  formats: ExportFormat[];
  /** Default format when not specified */
  defaultFormat: ExportFormat;
  /** Maximum records per export */
  maxRecords: number;
  /** Default records per export */
  defaultRecords: number;
  /** Export rate limiting (exports per hour) */
  rateLimit?: number;
  /** Custom export templates */
  templates?: Record<string, {
    name: string;
    fields: string[];
    format: ExportFormat;
  }>;
}

// ==== Sort Configuration ====
export interface DatabaseSortConfig {
  field: string;
  order: 'asc' | 'desc';
}

// ==== Enhanced Database Configuration ====
export interface EnhancedDatabaseConfig {
  // Basic Information
  id: string;
  code: string;
  name: string;
  category: string;
  description?: string;
  
  // Access Control
  accessLevel: AccessLevel;
  isActive: boolean;
  sortOrder: number;
  
  // Database Mapping
  tableName: string;
  schemaName: string;
  
  // Performance Configuration
  connectionPool: ConnectionPool;
  queryTimeout: number; // milliseconds
  
  // Caching Strategy
  cacheStrategy: CacheStrategy;
  cacheTTL: number; // seconds
  
  // Index Optimization
  indexStrategy?: IndexStrategy;
  
  // Export Configuration
  exportConfig: ExportConfiguration;
  exportFormats: ExportFormat[];
  maxExportLimit: number;
  defaultExportLimit: number;
  
  // Default Sorting
  defaultSort?: DatabaseSortConfig[];
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
}

// ==== Configuration Validation ====
export interface ConfigValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// ==== Configuration Cache ====
export interface ConfigCacheEntry {
  config: EnhancedDatabaseConfig;
  cachedAt: number;
  expiresAt: number;
}

// ==== Database Connection Configuration ====
export interface DatabaseConnectionConfig {
  pool: ConnectionPool;
  maxConnections: number;
  idleTimeout: number;
  connectionTimeout: number;
  queryTimeout: number;
}

// ==== Performance Metrics ====
export interface DatabasePerformanceMetrics {
  avgQueryTime: number;
  totalQueries: number;
  cacheHitRate: number;
  errorRate: number;
  lastUpdated: Date;
}

// ==== Configuration Service Interface ====
export interface IDatabaseConfigService {
  /**
   * Get enhanced database configuration
   */
  getConfig(databaseCode: string): Promise<EnhancedDatabaseConfig>;
  
  /**
   * Validate configuration
   */
  validateConfig(config: Partial<EnhancedDatabaseConfig>): ConfigValidationResult;
  
  /**
   * Update configuration
   */
  updateConfig(databaseCode: string, updates: Partial<EnhancedDatabaseConfig>): Promise<void>;
  
  /**
   * Clear configuration cache
   */
  clearCache(databaseCode?: string): Promise<void>;
  
  /**
   * Get performance metrics
   */
  getMetrics(databaseCode: string): Promise<DatabasePerformanceMetrics>;
}

// ==== Default Configurations ====
export const DEFAULT_CONFIG: Partial<EnhancedDatabaseConfig> = {
  accessLevel: 'free',
  schemaName: 'public',
  connectionPool: 'default',
  queryTimeout: 30000,
  cacheStrategy: 'redis',
  cacheTTL: 300,
  exportFormats: ['csv', 'xlsx'],
  maxExportLimit: 10000,
  defaultExportLimit: 1000,
  isActive: true,
};

export const DEFAULT_EXPORT_CONFIG: ExportConfiguration = {
  formats: ['csv', 'xlsx'],
  defaultFormat: 'csv',
  maxRecords: 10000,
  defaultRecords: 1000,
  rateLimit: 10, // 10 exports per hour
};

// ==== Configuration Templates ====
export const CONFIG_TEMPLATES = {
  medical_device: {
    category: '医疗器械',
    indexStrategy: {
      searchIndexes: ['productName', 'companyName', 'registrationNumber'],
      compositeIndexes: [
        { fields: ['category', 'status'], name: 'category_status_idx' },
        { fields: ['companyName', 'productName'], name: 'company_product_idx' }
      ],
      fullTextSearch: {
        fields: ['productName', 'description', 'intendedUse'],
        language: 'chinese'
      }
    }
  },
  pharmaceutical: {
    category: '药品信息',
    indexStrategy: {
      searchIndexes: ['drugName', 'manufacturer', 'approvalNumber'],
      compositeIndexes: [
        { fields: ['drugName', 'dosageForm'], name: 'drug_dosage_idx' },
        { fields: ['manufacturer', 'approvalDate'], name: 'mfg_approval_idx' }
      ]
    }
  },
  clinical_trial: {
    category: '临床试验',
    indexStrategy: {
      searchIndexes: ['trialId', 'title', 'sponsor'],
      compositeIndexes: [
        { fields: ['phase', 'status'], name: 'phase_status_idx' },
        { fields: ['sponsor', 'startDate'], name: 'sponsor_date_idx' }
      ]
    }
  }
} as const;

// ==== Type Guards ====
export function isValidAccessLevel(level: string): level is AccessLevel {
  return ['free', 'premium', 'enterprise'].includes(level);
}

export function isValidCacheStrategy(strategy: string): strategy is CacheStrategy {
  return ['redis', 'memory', 'none'].includes(strategy);
}

export function isValidExportFormat(format: string): format is ExportFormat {
  return ['csv', 'xlsx', 'json', 'xml'].includes(format);
}
