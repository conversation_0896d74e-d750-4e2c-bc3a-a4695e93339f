"use client";

import { useState, useEffect } from "react";
import AdvancedSearch from "@/components/AdvancedSearch";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface SearchCondition {
  id: string;
  field: string;
  value: string | string[] | { from?: string; to?: string };
  logic?: 'AND' | 'OR' | 'NOT';
}

interface FieldConfig {
  fieldName: string;
  displayName: string;
  fieldType: string;
  searchType: string;
  isAdvancedSearchable: boolean;
}

export default function TestAdvancedSearchPage() {
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentConditions, setCurrentConditions] = useState<SearchCondition[]>([]);
  const [availableFields, setAvailableFields] = useState<FieldConfig[]>([]);
  const [metadata] = useState({});

  // Mock field configurations for testing
  useEffect(() => {
    const mockFields: FieldConfig[] = [
      {
        fieldName: 'datereceived',
        displayName: 'Date Received',
        fieldType: 'date',
        searchType: 'date_range',
        isAdvancedSearchable: true
      },
      {
        fieldName: 'decisiondate',
        displayName: 'Decision Date',
        fieldType: 'date',
        searchType: 'date_range',
        isAdvancedSearchable: true
      },
      {
        fieldName: 'devicename',
        displayName: 'Device Name',
        fieldType: 'text',
        searchType: 'contains',
        isAdvancedSearchable: true
      },
      {
        fieldName: 'applicant',
        displayName: 'Applicant',
        fieldType: 'text',
        searchType: 'contains',
        isAdvancedSearchable: true
      },
      {
        fieldName: 'decision',
        displayName: 'Decision',
        fieldType: 'select',
        searchType: 'exact',
        isAdvancedSearchable: true
      }
    ];
    setAvailableFields(mockFields);
  }, []);

  const handleSearch = async (conditions: SearchCondition[]) => {
    setLoading(true);
    setCurrentConditions(conditions);
    
    try {
      // Test the actual API call
      const response = await fetch('/api/advanced-search/us_pmn', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          conditions,
          page: 1,
          limit: 10
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setSearchResults(data.data || []);
        console.log('Search successful:', data);
      } else {
        console.error('Search failed:', response.statusText);
        setSearchResults([]);
      }
    } catch (error) {
      console.error('Search error:', error);
      setSearchResults([]);
    } finally {
      setLoading(false);
    }
  };

  const handleClear = () => {
    setCurrentConditions([]);
    setSearchResults([]);
  };

  const formatConditionValue = (value: any) => {
    if (typeof value === 'object' && !Array.isArray(value)) {
      const { from, to } = value;
      if (from && to) return `${from} to ${to}`;
      if (from) return `from ${from}`;
      if (to) return `to ${to}`;
      return 'No date range';
    }
    if (Array.isArray(value)) return value.join(', ');
    return value || 'Empty';
  };

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <h1 className="text-2xl font-bold mb-6">Advanced Search Test Page</h1>
      
      <div className="space-y-6">
        {/* Advanced Search Component */}
        <Card>
          <CardHeader>
            <CardTitle>Advanced Search Component</CardTitle>
          </CardHeader>
          <CardContent>
            <AdvancedSearch
              onSearch={handleSearch}
              onClear={handleClear}
              availableFields={availableFields}
              currentConditions={currentConditions}
              metadata={metadata}
              metadataWithCounts={{
                decision: [
                  { value: 'Approved', count: 150 },
                  { value: 'Pending', count: 45 },
                  { value: 'Rejected', count: 12 }
                ]
              }}
            />
          </CardContent>
        </Card>

        {/* Current Search Conditions Display */}
        {currentConditions.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Current Search Conditions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {currentConditions.map((condition, index) => (
                  <div key={condition.id} className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                    {index > 0 && (
                      <Badge variant="outline" className="text-xs">
                        {condition.logic || 'AND'}
                      </Badge>
                    )}
                    <Badge variant="secondary">
                      {availableFields.find(f => f.fieldName === condition.field)?.displayName || condition.field}
                    </Badge>
                    <span className="text-sm">:</span>
                    <span className="text-sm font-mono bg-white px-2 py-1 rounded border">
                      {formatConditionValue(condition.value)}
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Search Results */}
        <Card>
          <CardHeader>
            <CardTitle>Search Results</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-sm text-gray-600">Searching...</p>
              </div>
            ) : searchResults.length > 0 ? (
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Found {searchResults.length} results:</p>
                <div className="max-h-96 overflow-y-auto">
                  {searchResults.map((result, index) => (
                    <div key={index} className="p-3 border rounded bg-gray-50">
                      <pre className="text-xs overflow-x-auto">
                        {JSON.stringify(result, null, 2)}
                      </pre>
                    </div>
                  ))}
                </div>
              </div>
            ) : currentConditions.length > 0 ? (
              <p className="text-gray-500 text-center py-4">No results found</p>
            ) : (
              <p className="text-gray-500 text-center py-4">Use Advanced Search above to test the functionality</p>
            )}
          </CardContent>
        </Card>

        {/* Test Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Test Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 text-sm">
              <div>
                <h4 className="font-semibold mb-2">🎯 Date Picker Testing Steps:</h4>
                <ol className="list-decimal list-inside space-y-1 ml-4">
                  <li>Click "Advanced Search" button to open the dialog</li>
                  <li>Click "Add Condition" to create a search condition</li>
                  <li>Select "Date Received" or "Decision Date" from the Field dropdown</li>
                  <li>In the Value section, you should see two date input fields side by side</li>
                  <li>Click on "Start Date" input - native browser date picker should open</li>
                  <li>Select a date and verify it appears in the field</li>
                  <li>Click on "End Date" input and select another date</li>
                  <li>Click the ❌ clear buttons to test individual field clearing</li>
                  <li>Click "Search" to test the API integration</li>
                </ol>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">✅ Expected Behavior:</h4>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li>Date fields should be horizontally aligned (side by side)</li>
                  <li>Native browser date picker should open when clicking input fields</li>
                  <li>Clear buttons should appear only when dates are selected</li>
                  <li>Layout should be responsive (stack on mobile)</li>
                  <li>Search conditions should display correctly in the results section</li>
                  <li>API calls should work with the date range format</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
