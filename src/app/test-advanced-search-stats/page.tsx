"use client";

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import AdvancedSearch, { type SearchCondition } from '@/components/AdvancedSearch';

export default function TestAdvancedSearchStats() {
  const [metadata, setMetadata] = useState<Record<string, string[]>>({});
  const [metadataWithCounts, setMetadataWithCounts] = useState<Record<string, Array<{ value: string; count: number }>>>({});
  const [conditions, setConditions] = useState<SearchCondition[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchMetadata = async () => {
      try {
        const response = await fetch('/api/meta/us_pmn');
        const data = await response.json();
        
        if (data.success) {
          setMetadata(data.data);
          setMetadataWithCounts(data.dataWithCounts);
          console.log('Metadata loaded:', data.data);
          console.log('MetadataWithCounts loaded:', data.dataWithCounts);
        }
      } catch (error) {
        console.error('Failed to fetch metadata:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchMetadata();
  }, []);

  const handleSearch = (searchConditions: SearchCondition[]) => {
    console.log('Search conditions:', searchConditions);
    setConditions(searchConditions);
  };

  const handleClear = () => {
    setConditions([]);
  };

  const availableFields = [
    {
      fieldName: 'thirdparty',
      displayName: 'Third Party',
      fieldType: 'text',
      filterType: 'multi_select',
      isAdvancedSearchable: true
    },
    {
      fieldName: 'reviewadvisecomm',
      displayName: 'Review Advisory Committee',
      fieldType: 'text',
      filterType: 'multi_select',
      isAdvancedSearchable: true
    }
  ];

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">Loading...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Advanced Search Statistics Test</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold mb-2">Third Party Statistics:</h3>
              <pre className="bg-gray-100 p-2 rounded text-sm">
                {JSON.stringify(metadataWithCounts.thirdparty, null, 2)}
              </pre>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold mb-2">Review Advisory Committee Statistics:</h3>
              <pre className="bg-gray-100 p-2 rounded text-sm">
                {JSON.stringify(metadataWithCounts.reviewadvisecomm?.slice(0, 5), null, 2)}
              </pre>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Advanced Search Component Test</CardTitle>
        </CardHeader>
        <CardContent>
          <AdvancedSearch
            onSearch={handleSearch}
            onClear={handleClear}
            availableFields={availableFields}
            currentConditions={conditions}
            metadata={metadata}
            metadataWithCounts={metadataWithCounts}
          />
        </CardContent>
      </Card>

      {conditions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Current Search Conditions</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-gray-100 p-2 rounded text-sm">
              {JSON.stringify(conditions, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
