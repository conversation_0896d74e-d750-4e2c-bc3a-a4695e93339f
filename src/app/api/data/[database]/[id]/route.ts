import { type NextRequest, NextResponse } from 'next/server';
import { getDynamicTable, isDrizzleTable, validateDatabaseCode } from '@/lib/drizzleTableMapping';
import { getDatabaseAccessLevel } from '@/lib/permissions';
import { checkPermissions } from '@/lib/server/permissions';
import { db } from '@/lib/db-server';
import { eq } from 'drizzle-orm';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ database: string; id: string }> }
) {
  try {
    const { database, id } = await params;

    // 输入验证
    if (!id || typeof id !== 'string') {
      return NextResponse.json(
        { success: false, error: 'Invalid item ID' },
        { status: 400 }
      );
    }

    // 使用新的统一验证函数
    const validation = await validateDatabaseCode(database);
    if (!validation.isValid) {
      return NextResponse.json(
        { success: false, error: validation.error },
        { status: validation.status || 400 }
      );
    }

    // 权限检查
    const requiredLevel = await getDatabaseAccessLevel(database) as "free" | "premium" | "enterprise";
    const hasAccess = await checkPermissions(requiredLevel);
    if (!hasAccess) {
      return NextResponse.json(
        { success: false, error: 'Permission denied' },
        { status: 403 }
      );
    }

    // 使用动态模型获取
    const table = await getDynamicTable(database);
    if (!isDrizzleTable(table)) {
      return NextResponse.json(
        { success: false, error: 'Model not found or invalid' },
        { status: 500 }
      );
    }

    // 查询数据 - 使用Drizzle语法
    const items = await db
      .select()
      .from(table)
      .where(eq(table.id, id))
      .limit(1);

    if (!items || items.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Item not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: items[0],
    });

  } catch (__error) {
    console.error('API Error:', __error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}