/**
 * 新的基于Drizzle的统一搜索API
 * 替代原有的Prisma搜索逻辑
 */

import { NextRequest, NextResponse } from 'next/server';
import { DrizzleSearchService } from '@/lib/services/drizzleSearchService';
import { validateDatabaseCode } from '@/lib/drizzleTableMapping';

/**
 * POST /api/drizzle-search/[database]
 * 统一搜索接口 - 支持条件搜索和全局关键词搜索
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database } = await params;
    
    // 验证数据库代码
    const validation = validateDatabaseCode(database);
    if (!validation.isValid) {
      return NextResponse.json({
        success: false,
        error: validation.error || `不支持的数据库代码: ${database}`
      }, { status: validation.status || 400 });
    }
    
    const body = await request.json();
    const {
      conditions = [],
      globalKeyword,
      page = 1,
      limit = 20,
      sortBy,
      sortOrder = 'desc'
    } = body;
    
    console.log('[DrizzleSearch API] 搜索请求:', {
      database,
      conditions: conditions.length,
      globalKeyword,
      page,
      limit,
      sortBy,
      sortOrder
    });
    
    // 执行搜索
    const result = await DrizzleSearchService.search({
      database,
      conditions,
      globalKeyword,
      page,
      limit,
      sortBy,
      sortOrder
    });
    
    console.log('[DrizzleSearch API] 搜索结果:', {
      success: result.success,
      dataCount: result.data.length,
      total: result.pagination.total,
      searchTime: result.searchInfo.searchTime
    });
    
    return NextResponse.json(result);
    
  } catch (error) {
    console.error('[DrizzleSearch API] 错误:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '搜索失败',
      data: [],
      pagination: { page: 1, limit: 20, total: 0, totalPages: 0 },
      searchInfo: {
        database: '',
        conditions: [],
        searchTime: 0
      }
    }, { status: 500 });
  }
}

/**
 * GET /api/drizzle-search/[database]
 * 简单搜索接口 - 支持URL参数
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database } = await params;
    const { searchParams } = new URL(request.url);
    
    // 验证数据库代码
    const validation = validateDatabaseCode(database);
    if (!validation.isValid) {
      return NextResponse.json({
        success: false,
        error: validation.error || `不支持的数据库代码: ${database}`
      }, { status: validation.status || 400 });
    }
    
    // 解析查询参数
    const globalKeyword = searchParams.get('q') || undefined;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const sortBy = searchParams.get('sortBy') || undefined;
    const sortOrder = (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc';
    
    // 解析filters参数
    let conditions: any[] = [];
    const filtersParam = searchParams.get('filters');
    if (filtersParam) {
      try {
        const filters = JSON.parse(filtersParam);
        conditions = Object.entries(filters).map(([field, value], index) => ({
          id: `filter_${index}`,
          field,
          value,
          logic: index === 0 ? undefined : 'AND'
        }));
      } catch (error) {
        console.warn('[DrizzleSearch API] 解析filters参数失败:', error);
      }
    }
    
    console.log('[DrizzleSearch API] GET搜索请求:', {
      database,
      globalKeyword,
      conditions: conditions.length,
      page,
      limit
    });
    
    // 执行搜索
    const result = await DrizzleSearchService.search({
      database,
      conditions,
      globalKeyword,
      page,
      limit,
      sortBy,
      sortOrder
    });
    
    return NextResponse.json(result);
    
  } catch (error) {
    console.error('[DrizzleSearch API] GET错误:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '搜索失败',
      data: [],
      pagination: { page: 1, limit: 20, total: 0, totalPages: 0 },
      searchInfo: {
        database: '',
        conditions: [],
        searchTime: 0
      }
    }, { status: 500 });
  }
}
