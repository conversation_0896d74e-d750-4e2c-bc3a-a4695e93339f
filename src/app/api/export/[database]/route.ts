import { type NextRequest, NextResponse } from 'next/server';
import { getDatabaseConfig } from '@/lib/configCache';
import { getDynamicTable, validateDatabaseCode, isDrizzleTable } from '@/lib/drizzleTableMapping';
import { buildDrizzleWhere } from '@/lib/server/buildDrizzleWhere';
import { getDatabaseAccessLevel } from '@/lib/permissions';
import { checkPermissions } from '@/lib/server/permissions';
import { getDatabaseExportLimits, validateExportLimit, isSupportedExportFormat, getExportConfigSummary } from '@/lib/exportConfig';
import * as XLSX from 'xlsx';
import { formatExportDate } from '@/lib/utils';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database: rawDatabase } = await params;
    
    // 统一转换为小写格式
    const database = rawDatabase.toLowerCase();

    console.error('导出请求数据库:', database, '原始代码:', rawDatabase);

    // 验证数据库代码
    const validation = await validateDatabaseCode(database);
    if (!validation.isValid) {
      return NextResponse.json(
        { success: false, error: validation.error },
        { status: validation.status || 400 }
      );
    }

    // 权限检查
    const requiredLevel = await getDatabaseAccessLevel(database);
    const hasAccess = await checkPermissions(requiredLevel);
    if (!hasAccess) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions, please upgrade membership' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const format = searchParams.get('format') || 'csv'; // csv, excel, json
    const requestedLimit = searchParams.get('limit') ? Number.parseInt(searchParams.get('limit')!) : undefined;

    // 获取导出配置
    const exportLimits = await getDatabaseExportLimits(database);

    // 验证导出格式
    if (!isSupportedExportFormat(format, exportLimits)) {
      return NextResponse.json(
        { success: false, error: `不支持的导出格式: ${format}` },
        { status: 400 }
      );
    }

    // 验证和调整导出限制
    const limit = validateExportLimit(requestedLimit, exportLimits);

    console.error(getExportConfigSummary(database, exportLimits, limit));

    // 获取字段配置
    let config;
    try {
      config = await getDatabaseConfig(database);
      console.error('配置已加载:', !!config);
    } catch (_configError) {
      console.error('配置加载错误:', _configError);
      return NextResponse.json(
        { success: false, error: '无法加载数据库配置' },
        { status: 500 }
      );
    }

    if (!config) {
      return NextResponse.json(
        { success: false, error: '数据库配置未找到' },
        { status: 404 }
      );
    }

    // 使用动态模型获取
    const model = await getDynamicTable(database);
    if (!isDrizzleTable(model)) {
      console.error('模型未找到:', database);
      return NextResponse.json(
        { success: false, error: '模型未找到' },
        { status: 404 }
      );
    }

    // 构建查询条件 - 过滤掉导出相关的参数
    const filteredParams = new URLSearchParams();
    for (const [key, value] of searchParams.entries()) {
      // 排除导出相关的参数，只保留数据筛选参数
      if (key !== 'format' && key !== 'limit') {
        filteredParams.append(key, value);
      }
    }

    const where = buildDrizzleWhere(filteredParams, config, model);
    console.error('查询条件键:', where ? Object.keys(where) : []);

    // 获取可导出字段
    let exportableFields;
    try {
      exportableFields = config.fields
        ?.filter(f => (f as any).isActive !== false && ((f as any).isExportable !== false))
        ?.sort((a, b) => (a.exportOrder || a.listOrder || 0) - (b.exportOrder || b.listOrder || 0)) || [];
      console.error('可导出字段数量:', exportableFields.length);

      // 如果没有明确设置isExportable的字段，回退到使用isVisible字段
      if (exportableFields.length === 0) {
        console.error('未找到可导出字段，回退到使用可见字段');
        exportableFields = config.fields
          ?.filter(f => (f as any).isActive !== false && (f as any).isVisible)
          ?.sort((a, b) => a.listOrder - b.listOrder) || [];
      }
    } catch (_fieldsError) {
      console.error('字段处理错误:', _fieldsError);
      // 使用基本字段作为后备
      exportableFields = [
        { fieldName: 'productName', displayName: 'Product Name', fieldType: 'text' },
        { fieldName: 'companyName', displayName: 'Company Name', fieldType: 'text' },
        { fieldName: 'approvalDate', displayName: 'Approval Date', fieldType: 'date' },
      ];
    }

    // 查询数据
    let data;
    try {
      // 构建select对象
      const select: Record<string, boolean> = {};
      exportableFields.forEach(field => {
        select[field.fieldName] = true;
      });
      select['id'] = true; // 主键始终包含，但不再包含database字段

      console.error('查询select字段:', Object.keys(select));

      data = await (model as any).findMany({
        where,
        select,
        take: limit,
        orderBy: { id: 'desc' },
      });

      console.error('数据查询成功，数量:', data.length);
    } catch (_queryError) {
      console.error('数据查询错误:', _queryError);
      return NextResponse.json(
        { success: false, error: '数据查询失败' },
        { status: 500 }
      );
    }

    if (!data || data.length === 0) {
      return NextResponse.json(
        { success: false, error: '未找到导出数据' },
        { status: 404 }
      );
    }

    if (format === 'csv') {
      // CSV 导出
      const headers = exportableFields.map(f => (f as any).exportDisplayName || f.displayName).join(',');
      const rows = data.map((item: Record<string, unknown>) =>
        exportableFields.map(f => {
          const value = item[f.fieldName];
          if (value === null || value === undefined) return '';

          // 格式化日期字段
          if (f.fieldType === 'date' && value) {
            const formattedDate = formatExportDate(value as string | Date);
            if (formattedDate.includes(',')) {
              return `"${formattedDate.replace(/"/g, '""')}"`;
            }
            return formattedDate;
          }

          if (typeof value === 'string' && value.includes(',')) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return String(value);
        }).join(',')
      );

      const csv = [headers, ...rows].join('\n');

      return new NextResponse(csv, {
        headers: {
          'Content-Type': 'text/csv; charset=utf-8',
          'Content-Disposition': `attachment; filename="${database}_export.csv"`,
        },
      });
    } else if (format === 'excel') {
      // Excel 导出
      const worksheetData = [
        exportableFields.map(f => (f as any).exportDisplayName || f.displayName), // 表头
        ...data.map((item: Record<string, unknown>) =>
          exportableFields.map(f => {
            const value = item[f.fieldName];
            if (value === null || value === undefined) return '';

            // 格式化日期字段
            if (f.fieldType === 'date' && value) {
              return formatExportDate(value as string | Date);
            }

            return value;
          })
        )
      ];

      const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, database);

      const excelBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

      return new NextResponse(excelBuffer, {
        headers: {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'Content-Disposition': `attachment; filename="${database}_export.xlsx"`,
        },
      });
    } else {
      // JSON 导出
      return NextResponse.json({
        success: true,
        data,
        meta: {
          count: data.length,
          fields: exportableFields,
          database: database,
          exportedAt: new Date().toISOString(),
        },
      });
    }
    
  } catch (__error) {
    console.error('导出 API 错误:', __error);
    return NextResponse.json(
      { success: false, error: '导出过程中发生错误' },
      { status: 500 }
    );
  }
}
