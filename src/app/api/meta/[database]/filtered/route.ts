import { NextRequest, NextResponse } from 'next/server';
import { getDynamicTable, isDrizzleTable } from '@/lib/drizzleTableMapping';
import { getDatabaseConfig } from '@/lib/configCache';
import { buildAdvancedSearchWhere } from '@/lib/server/buildDrizzleWhere';
import { db } from '@/lib/db-server';
import { count, ne, desc, and } from 'drizzle-orm';
import type { SearchCondition } from '@/components/AdvancedSearch';

/**
 * POST /api/meta/[database]/filtered
 * 获取基于搜索条件过滤的metadata
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const resolvedParams = await params;
    const database = resolvedParams.database;

    // 获取动态表
    const table = await getDynamicTable(database);
    if (!isDrizzleTable(table)) {
      return NextResponse.json(
        { success: false, error: 'Table not found or invalid' },
        { status: 500 }
      );
    }

    // 获取配置
    const config = await getDatabaseConfig(database);
    
    // 解析请求体
    const body = await request.json();
    const { 
      conditions = [], 
      filters = {} 
    }: { 
      conditions?: SearchCondition[]; 
      filters?: Record<string, any>; 
    } = body;

    console.log('[Filtered Metadata API] 接收到的条件:', { conditions, filters });

    // 构建where条件 - 优先使用Advanced Search条件
    let _baseWhere;
    if (conditions && conditions.length > 0) {
      // 使用Advanced Search条件
      _baseWhere = buildAdvancedSearchWhere(conditions, config, table);
    } else {
      // 回退到传统的filter参数
      const searchParams = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          value.forEach(v => searchParams.append(key, String(v)));
        } else {
          searchParams.set(key, String(value));
        }
      });
      const { buildDrizzleWhere } = await import('@/lib/server/buildDrizzleWhere');
      _baseWhere = buildDrizzleWhere(searchParams, config, table);
    }

    // 获取可筛选的字段
    const filterableFields = config.fields.filter(field => field.isFilterable);
    
    // 为每个可筛选字段生成带计数的选项
    const dataWithCounts: Record<string, Array<{ value: string; count: number }>> = {};

    for (const fieldConfig of filterableFields) {
      const fieldName = fieldConfig.fieldName;
      const fieldColumn = table[fieldName];

      if (!fieldColumn) {
        console.warn(`字段 ${fieldName} 在表中不存在，跳过metadata生成`);
        continue;
      }

      try {
        // 为当前字段构建WHERE条件时，需要排除对该字段本身的过滤
        // 这样可以显示该字段的所有可能值及其在当前过滤条件下的计数
        let fieldSpecificWhere;

        if (conditions && conditions.length > 0) {
          // 如果有Advanced Search条件，使用这些条件但排除当前字段的条件
          const filteredConditions = conditions.filter(cond => cond.field !== fieldName);
          if (filteredConditions.length > 0) {
            fieldSpecificWhere = buildAdvancedSearchWhere(filteredConditions, config, table);
          }
        } else if (filters && Object.keys(filters).length > 0) {
          // 如果有Filter Panel条件，使用这些条件但排除当前字段的过滤
          const filteredFilters = { ...filters };
          delete filteredFilters[fieldName];

          if (Object.keys(filteredFilters).length > 0) {
            const searchParams = new URLSearchParams();
            Object.entries(filteredFilters).forEach(([key, value]) => {
              if (Array.isArray(value)) {
                value.forEach(v => searchParams.append(key, String(v)));
              } else {
                searchParams.set(key, String(value));
              }
            });
            const { buildDrizzleWhere } = await import('@/lib/server/buildDrizzleWhere');
            fieldSpecificWhere = buildDrizzleWhere(searchParams, config, table);
          }
        }

        // 构建该字段的查询条件（排除null值）
        const fieldWhere = fieldSpecificWhere
          ? [fieldSpecificWhere, ne(fieldColumn, null)]
          : [ne(fieldColumn, null)];

        // 查询该字段的所有唯一值及其计数
        const fieldResults = await db
          .select({
            value: fieldColumn,
            count: count()
          })
          .from(table)
          .where(fieldWhere.length > 1 ?
            // 使用 and() 函数正确组合多个条件
            and(...fieldWhere) :
            fieldWhere[0]
          )
          .groupBy(fieldColumn)
          .orderBy(desc(count()))
          .limit(1000); // 限制返回数量，避免性能问题

        // 转换为所需格式
        dataWithCounts[fieldName] = fieldResults
          .filter(item => item.value !== null && item.value !== '')
          .map(item => ({
            value: String(item.value),
            count: Number(item.count)
          }));

        console.log(`[Filtered Metadata] ${fieldName}: ${dataWithCounts[fieldName].length} 个选项`);

      } catch (error) {
        console.error(`[Filtered Metadata] 处理字段 ${fieldName} 时出错:`, error);
        // 继续处理其他字段
        dataWithCounts[fieldName] = [];
      }
    }

    return NextResponse.json({
      success: true,
      dataWithCounts,
      appliedConditions: conditions,
      appliedFilters: filters,
      databaseInfo: {
        code: database,
        totalFields: config.fields.length,
        filterableFields: filterableFields.length,
      }
    });

  } catch (error) {
    console.error('Filtered Metadata API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
