import { NextResponse } from 'next/server';
import { getDatabaseConfig } from '@/lib/configCache';
import type { DatabaseConfig } from '@/lib/configCache';
import { getDynamicTable, validateDatabaseCode, isDrizzleTable } from '@/lib/drizzleTableMapping';
import { db } from '@/lib/db-server';
import { count, isNull, or, eq, ne, desc, and, not } from 'drizzle-orm';



export async function GET(
  request: Request,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database: rawDatabase } = await params;
    
    // 统一转换为小写格式
    const database = rawDatabase.toLowerCase();
    
    // 验证数据库代码
    const validation = await validateDatabaseCode(database);
    if (!validation.isValid) {
      return NextResponse.json(
        { success: false, error: validation.error },
        { status: validation.status || 400 }
      );
    }

    // 使用动态模型获取 - 重构版本：完全不依赖database字段
    const model = await getDynamicTable(database);
    if (!isDrizzleTable(model)) {
      return NextResponse.json(
        { success: false, error: '模型未找到或无效' },
        { status: 500 }
      );
    }

    // 获取配置
    const config: DatabaseConfig = await getDatabaseConfig(database);
    // 只返回配置表中isFilterable的字段
    const metaFields = config.fields.filter(f => f.isFilterable);
    const metadata: Record<string, string[]> = {};
    const metadataWithCounts: Record<string, Array<{ value: string; count: number }>> = {};

    for (const fieldConfig of metaFields) {
      const field = fieldConfig.fieldName;
      
      // select和multi_select类型做distinct，date_range类型不需要distinct
      if (fieldConfig.filterType === 'select' || fieldConfig.filterType === 'multi_select') {
        try {
          // 使用 Drizzle ORM 进行数据聚合
          const fieldColumn = model[field];

          if (!fieldColumn) {
            console.warn(`字段 ${field} 在表中不存在，跳过`);
            metadata[field] = [];
            metadataWithCounts[field] = [];
            continue;
          }

          // 1. 获取非空值的计数 - 使用 Drizzle 语法
          const groupedValues = await db
            .select({
              value: fieldColumn,
              count: count()
            })
            .from(model)
            .where(
              and(
                ne(fieldColumn, ''),
                not(isNull(fieldColumn))
              )
            )
            .groupBy(fieldColumn)
            .orderBy(desc(count()))
            .limit(200);

          // 2. 单独统计空值（null和空字符串）
          const nullCountResult = await db
            .select({ count: count() })
            .from(model)
            .where(
              or(
                isNull(fieldColumn),
                eq(fieldColumn, '')
              )
            );

          const nullCount = nullCountResult[0]?.count || 0;

          // 3. 合并非空值和空值的结果
          const nonNullValues = groupedValues.map(item => ({
            value: String(item.value || '').trim(),
            count: Number(item.count) || 0,
            isNull: false
          }));

          // 4. 添加N/A项（如果有空值）
          const valuesWithCounts = [...nonNullValues];
          if (nullCount > 0) {
            valuesWithCounts.push({
              value: 'N/A',
              count: Number(nullCount),
              isNull: true
            });
          }

          // 5. 排序：按计数倒序，N/A项排在最后
          valuesWithCounts.sort((a, b) => {
            if (a.isNull && !b.isNull) return 1;
            if (!a.isNull && b.isNull) return -1;
            return b.count - a.count; // 按计数倒序排列
          });

          // 保持向后兼容性，移除内部使用的isNull字段
          metadata[field] = valuesWithCounts.map(item => item.value);
          metadataWithCounts[field] = valuesWithCounts.map(item => ({
            value: item.value,
            count: item.count
          }));
          
        } catch (__error) {
          console.error(`获取字段 ${field} 元数据失败:`, __error);

          // 回退到简单查询方式 - 使用 Drizzle 语法
          try {
            const fieldColumn = model[field];

            if (!fieldColumn) {
              metadata[field] = [];
              metadataWithCounts[field] = [];
              continue;
            }

            const distinctValues = await db
              .selectDistinct({ value: fieldColumn })
              .from(model)
              .where(
                and(
                  not(isNull(fieldColumn)),
                  ne(fieldColumn, '')
                )
              )
              .orderBy(fieldColumn)
              .limit(100);

            const values = distinctValues
              .map(item => String(item.value || '').trim())
              .filter(value => value !== '' && value.replace(/\s/g, '') !== '')
              .filter((value, index, self) => self.indexOf(value) === index);

            metadata[field] = values;
            metadataWithCounts[field] = values.map(value => ({ value, count: 0 }));

          } catch (_fallbackError) {
            console.error(`字段 ${field} 回退查询也失败:`, _fallbackError);
            // 对于出错的字段，返回空数组
            metadata[field] = [];
            metadataWithCounts[field] = [];
          }
        }
      } else if (fieldConfig.filterType === 'date_range') {
        // 日期区间类型不需要distinct，前端只需知道有此筛选项
        metadata[field] = [];
        metadataWithCounts[field] = [];
      }
    }

    return NextResponse.json({
      success: true,
      data: metadata,
      dataWithCounts: metadataWithCounts,
      config, // 返回当前使用的配置，便于前端调试
      databaseInfo: {
        code: database,
        originalCode: rawDatabase, // 保留原始代码用于调试
      }
    });
    
  } catch (__error) {
    console.error(`Metadata API error:`, __error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
} 