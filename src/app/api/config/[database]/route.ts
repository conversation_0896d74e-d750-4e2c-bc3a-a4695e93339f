import { type NextRequest, NextResponse } from 'next/server';
import { getDatabaseConfig } from '@/lib/configCache';
import { validateDatabaseCode } from '@/lib/drizzleTableMapping';

export const dynamic = 'force-dynamic';

/**
 * GET /api/config/[database]
 * 获取数据库配置信息
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database } = await params;

    // 验证数据库代码
    if (!validateDatabaseCode(database)) {
      return NextResponse.json({
        success: false,
        error: `不支持的数据库代码: ${database}`
      }, { status: 400 });
    }

    // 获取配置
    const config = await getDatabaseConfig(database);

    return NextResponse.json({
      success: true,
      data: config
    });

  } catch (error) {
    console.error('[Config API] 错误:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取配置失败'
    }, { status: 500 });
  }
}
