import { NextResponse } from 'next/server';
import { db } from '@/lib/prisma';

// 数据库配置缓存
let databaseConfigsCache: Record<string, any> | null = null;
let configsCacheExpiry = 0;
const CACHE_TTL = 10 * 60 * 1000; // 10分钟缓存

// 预加载Promise，避免并发请求时重复查询数据库
let loadingPromise: Promise<Record<string, any>> | null = null;



export async function GET() {
  try {
    const now = Date.now();

    // 如果缓存有效，直接返回
    if (databaseConfigsCache && now < configsCacheExpiry) {
      return NextResponse.json({
        success: true,
        data: databaseConfigsCache
      });
    }

    // 如果正在加载中，等待现有的加载完成
    if (loadingPromise) {
      const data = await loadingPromise;
      return NextResponse.json({
        success: true,
        data
      });
    }

    // 创建新的加载Promise
    loadingPromise = loadDatabaseConfigs();

    try {
      const data = await loadingPromise;
      return NextResponse.json({
        success: true,
        data
      });
    } finally {
      loadingPromise = null;
    }

  } catch (error) {
    console.error('获取数据库配置失败:', error);
    loadingPromise = null;

    // 返回错误，不再使用硬编码回退
    return NextResponse.json({
      success: false,
      error: '无法获取数据库配置，请检查数据库连接',
      data: {}
    }, { status: 500 });
  }
}

// 独立的数据库配置加载函数
async function loadDatabaseConfigs(): Promise<Record<string, any>> {
  const configs = await db.databaseConfig.findMany({
    where: { isActive: true },
    select: {
      code: true,
      name: true,
      category: true,
      description: true,
      accessLevel: true,
      defaultSort: true,
      sortOrder: true,
      exportConfig: true
    },
    orderBy: { sortOrder: 'asc' }
  });

  // 构建配置对象
  const configsMap: Record<string, any> = {};
  configs.forEach(config => {
    // 从 exportConfig 中获取图标和排序信息
    const exportConfig = config.exportConfig as any || {};
    const icon = exportConfig.icon || '📊';
    const categoryOrder = exportConfig.categoryOrder || config.sortOrder || 99;
    const orderInCategory = exportConfig.orderInCategory || 1;

    configsMap[config.code] = {
      name: config.name,
      category: config.category,
      description: config.description || '',
      accessLevel: config.accessLevel,
      defaultSort: config.defaultSort || null,
      sortOrder: config.sortOrder,
      categoryOrder: categoryOrder,
      orderInCategory: orderInCategory,
      icon: icon,
    };
  });

  // 设置缓存
  const now = Date.now();
  databaseConfigsCache = configsMap;
  configsCacheExpiry = now + CACHE_TTL;

  return configsMap;
}
