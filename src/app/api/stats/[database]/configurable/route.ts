import { NextRequest, NextResponse } from 'next/server';
import { getDynamicTable, isDrizzleTable } from '@/lib/drizzleTableMapping';
import { getDatabaseConfig } from '@/lib/configCache';
import { buildDrizzleWhere, buildAdvancedSearchWhere } from '@/lib/server/buildDrizzleWhere';
import { db } from '@/lib/db-server';
import { count, eq, and, ne, desc, asc, sum, avg, min, max } from 'drizzle-orm';
import type { SearchCondition } from '@/components/AdvancedSearch';

/**
 * 可配置的统计API
 * 根据fieldConfig中的统计配置动态生成统计数据
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const resolvedParams = await params;
    const database = resolvedParams.database;

    // 获取动态表
    const table = await getDynamicTable(database);
    if (!isDrizzleTable(table)) {
      return NextResponse.json(
        { success: false, error: 'Table not found or invalid' },
        { status: 500 }
      );
    }

    // 获取配置
    const config = await getDatabaseConfig(database);
    const { searchParams } = new URL(request.url);

    // 构建where条件
    const where = buildDrizzleWhere(searchParams, config, table);

    // 获取启用统计的字段配置
    const statisticsFields = config.fields
      .filter(field => field.isStatisticsEnabled)
      .sort((a, b) => (a.statisticsOrder || 0) - (b.statisticsOrder || 0));

    if (statisticsFields.length === 0) {
      return NextResponse.json({
        success: true,
        data: {
          basic: { total: 0, active: 0, inactive: 0 },
          statistics: [],
        },
        message: 'No statistics fields configured'
      });
    }

    // 基础统计 - 使用 Drizzle 语法
    const totalCountResult = await db
      .select({ count: count() })
      .from(table)
      .where(where);
    const totalCount = Number(totalCountResult[0]?.count || 0);

    let activeCount = 0;
    try {
      // 检查是否有 isActive 字段
      if (table.isActive) {
        const activeWhere = where
          ? and(where, eq(table.isActive, true))
          : eq(table.isActive, true);

        const activeCountResult = await db
          .select({ count: count() })
          .from(table)
          .where(activeWhere);
        activeCount = Number(activeCountResult[0]?.count || 0);
      } else {
        activeCount = totalCount;
      }
    } catch (__error) {
      activeCount = totalCount; // 如果没有isActive字段，回退到总数
    }

    // 动态生成统计数据
    const statistics = [];
    
    for (const fieldConfig of statisticsFields) {
      const fieldName = fieldConfig.fieldName;
      const displayName = fieldConfig.statisticsDisplayName || fieldConfig.displayName;
      const statisticsType = fieldConfig.statisticsType || 'count';
      const config = fieldConfig.statisticsConfig || {};
      
      try {
        let statisticData: Record<string, unknown> | null = null;

        switch (statisticsType) {
          case 'count':
          case 'group_by':
            // 分组计数统计 - 使用 Drizzle 语法
            const limit = (config as any).limit || 50;
            const sortOrder = (fieldConfig as any).statisticsSortOrder || 'desc';

            // 获取字段列对象
            const fieldColumn = table[fieldName];
            if (!fieldColumn) {
              console.warn(`字段 ${fieldName} 在表中不存在，跳过统计`);
              break;
            }

            // 构建查询条件
            let groupWhere = where ? and(where, ne(fieldColumn, null)) : ne(fieldColumn, null);

            // 如果有 isActive 字段且活跃数量小于总数，则只统计活跃记录
            if (activeCount < totalCount && table.isActive) {
              groupWhere = groupWhere
                ? and(groupWhere, eq(table.isActive, true))
                : eq(table.isActive, true);
            }

            const groupByResults = await db
              .select({
                value: fieldColumn,
                count: count()
              })
              .from(table)
              .where(groupWhere)
              .groupBy(fieldColumn)
              .orderBy(sortOrder === 'asc' ? asc(count()) : desc(count()))
              .limit(limit);

            statisticData = {
              type: 'group_by',
              items: groupByResults.map((item) => ({
                name: item.value,
                count: Number(item.count),
              }))
            };
            break;

          case 'sum':
            // 求和统计（仅适用于数值字段）- 使用 Drizzle 语法
            if (fieldConfig.fieldType === 'number') {
              const fieldColumn = table[fieldName];
              if (!fieldColumn) {
                console.warn(`字段 ${fieldName} 在表中不存在，跳过求和统计`);
                break;
              }

              let sumWhere = where ? and(where, ne(fieldColumn, null)) : ne(fieldColumn, null);

              if (activeCount < totalCount && table.isActive) {
                sumWhere = sumWhere
                  ? and(sumWhere, eq(table.isActive, true))
                  : eq(table.isActive, true);
              }

              const sumResult = await db
                .select({
                  total: sum(fieldColumn),
                  count: count()
                })
                .from(table)
                .where(sumWhere);

              statisticData = {
                type: 'sum',
                total: Number(sumResult[0]?.total || 0),
                count: Number(sumResult[0]?.count || 0),
              };
            }
            break;

          case 'avg':
            // 平均值统计（仅适用于数值字段）- 使用 Drizzle 语法
            if (fieldConfig.fieldType === 'number') {
              const fieldColumn = table[fieldName];
              if (!fieldColumn) {
                console.warn(`字段 ${fieldName} 在表中不存在，跳过平均值统计`);
                break;
              }

              let avgWhere = where ? and(where, ne(fieldColumn, null)) : ne(fieldColumn, null);

              if (activeCount < totalCount && table.isActive) {
                avgWhere = avgWhere
                  ? and(avgWhere, eq(table.isActive, true))
                  : eq(table.isActive, true);
              }

              const avgResult = await db
                .select({
                  average: avg(fieldColumn),
                  count: count()
                })
                .from(table)
                .where(avgWhere);

              statisticData = {
                type: 'avg',
                average: Number(avgResult[0]?.average || 0),
                count: Number(avgResult[0]?.count || 0),
              };
            }
            break;

          case 'min_max':
            // 最值统计（适用于数值和日期字段）- 使用 Drizzle 语法
            if (fieldConfig.fieldType === 'number' || fieldConfig.fieldType === 'date') {
              const fieldColumn = table[fieldName];
              if (!fieldColumn) {
                console.warn(`字段 ${fieldName} 在表中不存在，跳过最值统计`);
                break;
              }

              let minMaxWhere = where ? and(where, ne(fieldColumn, null)) : ne(fieldColumn, null);

              if (activeCount < totalCount && table.isActive) {
                minMaxWhere = minMaxWhere
                  ? and(minMaxWhere, eq(table.isActive, true))
                  : eq(table.isActive, true);
              }

              const minMaxResult = await db
                .select({
                  min: min(fieldColumn),
                  max: max(fieldColumn),
                  count: count()
                })
                .from(table)
                .where(minMaxWhere);

              statisticData = {
                type: 'min_max',
                min: minMaxResult[0]?.min,
                max: minMaxResult[0]?.max,
                count: Number(minMaxResult[0]?.count || 0),
              };
            }
            break;
        }

        if (statisticData) {
          statistics.push({
            fieldName,
            displayName,
            statisticsType,
            statisticsDefaultLimit: fieldConfig.statisticsDefaultLimit || 5,
            statisticsMaxLimit: fieldConfig.statisticsMaxLimit || 50,
            data: statisticData,
            order: fieldConfig.statisticsOrder || 0,
          });
        }

      } catch (__error) {
        console.error(`统计字段 ${fieldName} 处理失败:`, __error);
        // 继续处理其他字段，不中断整个统计过程
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        basic: {
          total: totalCount,
          active: activeCount,
          inactive: totalCount - activeCount,
        },
        statistics,
      },
      appliedFilters: Object.fromEntries(searchParams.entries()),
      databaseInfo: {
        code: database,
        totalFields: config.fields.length,
        statisticsFields: statisticsFields.length,
      }
    });

  } catch (__error) {
    console.error('Configurable Statistics API error:', __error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST方法：支持Advanced Search条件的统计API
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const resolvedParams = await params;
    const database = resolvedParams.database;

    // 获取动态表
    const table = await getDynamicTable(database);
    if (!isDrizzleTable(table)) {
      return NextResponse.json(
        { success: false, error: 'Table not found or invalid' },
        { status: 500 }
      );
    }

    // 获取配置
    const config = await getDatabaseConfig(database);

    // 解析请求体
    const body = await request.json();
    const {
      conditions = [],
      filters = {}
    }: {
      conditions?: SearchCondition[];
      filters?: Record<string, any>;
    } = body;

    console.log('[Stats API POST] 接收到的条件:', { conditions, filters });

    // 构建where条件 - 优先使用Advanced Search条件
    let where;
    if (conditions && conditions.length > 0) {
      // 使用Advanced Search条件
      where = buildAdvancedSearchWhere(conditions, config, table);
    } else {
      // 回退到传统的filter参数
      const searchParams = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          value.forEach(v => searchParams.append(key, String(v)));
        } else {
          searchParams.set(key, String(value));
        }
      });
      where = buildDrizzleWhere(searchParams, config, table);
    }

    // 获取启用统计的字段配置
    const statisticsFields = config.fields
      .filter(field => field.isStatisticsEnabled)
      .sort((a, b) => (a.statisticsOrder || 0) - (b.statisticsOrder || 0));

    if (statisticsFields.length === 0) {
      return NextResponse.json({
        success: true,
        data: {
          basic: { total: 0, active: 0, inactive: 0 },
          statistics: [],
        },
        message: 'No statistics fields configured'
      });
    }

    // 基础统计 - 使用 Drizzle 语法
    const totalCountResult = await db
      .select({ count: count() })
      .from(table)
      .where(where);
    const totalCount = Number(totalCountResult[0]?.count || 0);

    let activeCount = 0;
    try {
      // 检查是否有 isActive 字段
      if (table.isActive) {
        const activeWhere = where
          ? and(where, eq(table.isActive, true))
          : eq(table.isActive, true);

        const activeCountResult = await db
          .select({ count: count() })
          .from(table)
          .where(activeWhere);
        activeCount = Number(activeCountResult[0]?.count || 0);
      } else {
        activeCount = totalCount;
      }
    } catch (__error) {
      activeCount = totalCount; // 如果没有isActive字段，回退到总数
    }

    // 动态生成统计数据（复用GET方法的逻辑）
    const statistics = [];

    for (const fieldConfig of statisticsFields) {
      const fieldName = fieldConfig.fieldName;
      const displayName = fieldConfig.statisticsDisplayName || fieldConfig.displayName;
      const statisticsType = fieldConfig.statisticsType || 'count';
      const config = fieldConfig.statisticsConfig || {};

      try {
        let statisticData: Record<string, unknown> | null = null;

        switch (statisticsType) {
          case 'count':
          case 'group_by':
            // 分组计数统计
            const limit = (config as any).limit || 50;
            const sortOrder = (fieldConfig as any).statisticsSortOrder || 'desc';

            const fieldColumn = table[fieldName];
            if (!fieldColumn) {
              console.warn(`字段 ${fieldName} 在表中不存在，跳过统计`);
              break;
            }

            let groupWhere = where ? and(where, ne(fieldColumn, null)) : ne(fieldColumn, null);

            if (activeCount < totalCount && table.isActive) {
              groupWhere = groupWhere
                ? and(groupWhere, eq(table.isActive, true))
                : eq(table.isActive, true);
            }

            const groupByResults = await db
              .select({
                value: fieldColumn,
                count: count()
              })
              .from(table)
              .where(groupWhere)
              .groupBy(fieldColumn)
              .orderBy(sortOrder === 'asc' ? asc(count()) : desc(count()))
              .limit(limit);

            statisticData = {
              type: 'group_by',
              items: groupByResults.map((item) => ({
                name: item.value,
                count: Number(item.count),
              }))
            };
            break;

          // 其他统计类型的处理逻辑与GET方法相同...
        }

        if (statisticData) {
          statistics.push({
            fieldName,
            displayName,
            statisticsType,
            statisticsDefaultLimit: fieldConfig.statisticsDefaultLimit || 5,
            statisticsMaxLimit: fieldConfig.statisticsMaxLimit || 50,
            data: statisticData,
            order: fieldConfig.statisticsOrder || 0,
          });
        }

      } catch (__error) {
        console.error(`统计字段 ${fieldName} 处理失败:`, __error);
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        basic: {
          total: totalCount,
          active: activeCount,
          inactive: totalCount - activeCount,
        },
        statistics,
      },
      appliedConditions: conditions,
      appliedFilters: filters,
      databaseInfo: {
        code: database,
        totalFields: config.fields.length,
        statisticsFields: statisticsFields.length,
      }
    });

  } catch (__error) {
    console.error('Configurable Statistics API POST error:', __error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
