import { type NextRequest, NextResponse } from 'next/server';
import { getDatabaseConfig } from '@/lib/configCache';
import { getDynamicTable, validateDatabaseCode, isDrizzleTable } from '@/lib/drizzleTableMapping';
import { buildAdvancedSearchWhere } from '@/lib/server/buildDrizzleWhere';
import { db } from '@/lib/db-server';
import { count, desc } from 'drizzle-orm';

export const dynamic = 'force-dynamic';

interface SearchCondition {
  id: string;
  field: string;
  value: string | string[] | { from?: string; to?: string };
  logic?: 'AND' | 'OR' | 'NOT';
}

interface FieldStatisticsRequest {
  fieldName: string;
  conditions?: SearchCondition[];
  limit?: number;
}

/**
 * POST /api/stats/[database]/field-statistics
 * 获取指定字段的统计数据，支持基于搜索条件的过滤
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  const startTime = Date.now();

  try {
    const { database } = await params;

    // 验证数据库代码
    if (!validateDatabaseCode(database)) {
      return NextResponse.json({
        success: false,
        error: `不支持的数据库代码: ${database}`
      }, { status: 400 });
    }

    const body: FieldStatisticsRequest = await request.json();
    const { fieldName, conditions = [], limit = 50 } = body;

    if (!fieldName) {
      return NextResponse.json({
        success: false,
        error: '字段名称不能为空'
      }, { status: 400 });
    }

    // 获取配置
    const config = await getDatabaseConfig(database);
    
    // 验证字段是否启用统计
    const fieldConfig = config.fields.find(f => f.fieldName === fieldName);
    if (!fieldConfig || !fieldConfig.isStatisticsEnabled) {
      return NextResponse.json({
        success: false,
        error: `字段 ${fieldName} 未启用统计功能`
      }, { status: 400 });
    }

    // 获取动态表
    const table = await getDynamicTable(database);
    if (!isDrizzleTable(table)) {
      return NextResponse.json({
        success: false,
        error: '模型未找到或无效'
      }, { status: 500 });
    }

    // 构建查询条件
    const where = conditions.length > 0 
      ? buildAdvancedSearchWhere(conditions, config, table)
      : undefined;

    // 获取字段统计数据
    const fieldColumn = table[fieldName];
    if (!fieldColumn) {
      return NextResponse.json({
        success: false,
        error: `字段 ${fieldName} 在表中不存在`
      }, { status: 400 });
    }

    // 执行统计查询
    let statisticsQuery = db
      .select({
        value: fieldColumn,
        count: count()
      })
      .from(table)
      .groupBy(fieldColumn)
      .orderBy(desc(count()))
      .limit(limit);

    if (where) {
      statisticsQuery = statisticsQuery.where(where) as any;
    }

    const statisticsResult = await statisticsQuery;

    // 处理结果
    const items = statisticsResult.map(item => ({
      value: item.value === null ? 'N/A' : String(item.value),
      count: Number(item.count)
    }));

    // 计算总数
    let totalQuery = db
      .select({ total: count() })
      .from(table);

    if (where) {
      totalQuery = totalQuery.where(where) as any;
    }

    const totalResult = await totalQuery;
    const total = Number(totalResult[0]?.total || 0);

    const processingTime = Date.now() - startTime;

    return NextResponse.json({
      success: true,
      data: {
        fieldName,
        items,
        total,
        itemCount: items.length,
        hasMore: items.length >= limit
      },
      meta: {
        database,
        processingTime,
        conditionsApplied: conditions.length,
        limit
      }
    });

  } catch (error) {
    console.error('[Field Statistics API] 错误:', error);
    
    const processingTime = Date.now() - startTime;
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取字段统计失败',
      meta: {
        processingTime
      }
    }, { status: 500 });
  }
}
