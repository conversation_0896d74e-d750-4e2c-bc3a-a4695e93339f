import { type NextRequest, NextResponse } from 'next/server';
import { getDatabaseConfig } from '@/lib/drizzleConfigCache';
import { getDynamicTable, validateDatabaseCode, isDrizzleTable } from '@/lib/drizzleTableMapping';
import { buildDrizzleWhere } from '@/lib/server/buildDrizzleWhere';

// 定义统计数据类型
interface YearlyStats {
  year: number;
  count: number;
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database: rawDatabase } = await params;
    
    // 统一转换为小写格式
    const database = rawDatabase.toLowerCase();

    // 验证数据库代码
    const validation = await validateDatabaseCode(database);
    if (!validation.isValid) {
      return NextResponse.json(
        { success: false, error: validation.error },
        { status: validation.status || 400 }
      );
    }

    // 权限检查 - 临时禁用用于测试
    /*
    const requiredLevel = await getDatabaseAccessLevel(database);
    const hasAccess = await checkPermissions(requiredLevel);
    if (!hasAccess) {
      return NextResponse.json(
        { success: false, error: 'Permission denied' },
        { status: 403 }
      );
    }
    */

    // 使用动态模型获取
    const model = await getDynamicTable(database);
    if (!isDrizzleTable(model)) {
      return NextResponse.json(
        { success: false, error: '模型未找到或无效' },
        { status: 500 }
      );
    }

    // 获取配置
    const config = await getDatabaseConfig(database);
    const { searchParams } = new URL(request.url);
    
    // 统一where条件 - 重构版本：不再需要database字段过滤
    const where = buildDrizzleWhere(searchParams, config, model);

    // 基础统计
    const totalCount = await (model as any).count({ where });
    
    let activeCount = 0;
    try {
      activeCount = await (model as any).count({ 
        where: { ...where, isActive: true } 
      });
    } catch (__error) {
      console.error('isActive字段不可用，跳过活跃记录统计');
      activeCount = totalCount; // 回退到总数
    }

    // 按类别统计（如果存在category字段）
    let categoryStats: Record<string, unknown>[] = [];
    try {
      // 获取category字段的排序配置
      const categoryField = config.fields.find(f => f.fieldName === 'category');
      const categorySortOrder = categoryField?.statisticsSortOrder || 'desc';

      categoryStats = await (model as any).groupBy({
        by: ['category'],
        where: { ...where, isActive: true, category: { not: null } },
        _count: { category: true },
        orderBy: { _count: { category: categorySortOrder } },
        take: 10,
      });
    } catch (__error) {
      console.error('category字段不可用于分组统计');
    }

    // 按企业统计（如果存在companyName字段）
    let companyStats: Record<string, unknown>[] = [];
    try {
      // 获取companyName字段的排序配置
      const companyField = config.fields.find(f => f.fieldName === 'companyName');
      const companySortOrder = companyField?.statisticsSortOrder || 'desc';

      companyStats = await (model as any).groupBy({
        by: ['companyName'],
        where: { ...where, isActive: true, companyName: { not: null } },
        _count: { companyName: true },
        orderBy: { _count: { companyName: companySortOrder } },
        take: 10,
      });
    } catch (__error) {
      console.error('companyName字段不可用于分组统计');
    }

    // 按管理类别统计（如果存在managementType字段）
    let managementTypeStats: Record<string, unknown>[] = [];
    try {
      managementTypeStats = await (model as any).groupBy({
        by: ['managementType'],
        where: { ...where, isActive: true, managementType: { not: null } },
        _count: { managementType: true },
        orderBy: { _count: { managementType: 'desc' } },
      });
    } catch (__error) {
      console.error('managementType字段不可用于分组统计');
    }

    // 按年份统计（如果存在approvalDate字段）
    let yearlyStats: YearlyStats[] = [];
    try {
      // 获取最近5年的数据
      const currentYear = new Date().getFullYear();
      const startYear = currentYear - 4;
      
      const yearlyData = await (model as any).findMany({
        where: {
          ...where,
          isActive: true,
          approvalDate: {
            gte: new Date(`${startYear}-01-01`),
            lte: new Date(`${currentYear}-12-31`),
          }
        },
        select: {
          approvalDate: true,
        }
      });

      // 按年份分组统计
      const yearCounts: Record<number, number> = {};
      yearlyData.forEach((item: Record<string, unknown>) => {
        if (item.approvalDate) {
          const year = new Date(item.approvalDate as string).getFullYear();
          yearCounts[year] = (yearCounts[year] || 0) + 1;
        }
      });

      yearlyStats = Object.entries(yearCounts)
        .map(([year, count]) => ({ year: parseInt(year), count }))
        .sort((a, b) => b.year - a.year);
        
    } catch (__error) {
      console.error('approvalDate field not available for annual statistics');
    }

    return NextResponse.json({
      success: true,
      data: {
        basic: {
          total: totalCount,
          active: activeCount,
          inactive: totalCount - activeCount,
        },
        categories: categoryStats,
        companies: companyStats,
        managementTypes: managementTypeStats,
        yearly: yearlyStats,
      },
      databaseInfo: {
        code: database,
        originalCode: rawDatabase,
      }
    });
    
  } catch (__error) {
    console.error('Statistics API error:', __error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
