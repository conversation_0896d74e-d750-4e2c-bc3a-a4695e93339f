import { type NextRequest, NextResponse } from 'next/server';
import { getDatabaseConfig } from '@/lib/configCache';
import type { DatabaseConfig } from '@/lib/configCache';
import { getDynamicTable, validateDatabaseCode, isDrizzleTable } from '@/lib/drizzleTableMapping';
import { validatePaginationParams, buildPaginationResponse } from '@/lib/globalPagination';
import { buildAdvancedSearchWhere } from '@/lib/server/buildDrizzleWhere';
import { db } from '@/lib/db-server';
import { count, desc, asc } from 'drizzle-orm';

export const dynamic = 'force-dynamic';

interface SearchCondition {
  id: string;
  field: string;
  value: string | string[] | { from?: string; to?: string };
  logic?: 'AND' | 'OR' | 'NOT';
}

interface AdvancedSearchRequest {
  conditions: SearchCondition[];
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  globalKeyword?: string; // 全局搜索关键词
}

// 注意：现在使用 buildAdvancedSearchWhere 从 buildDrizzleWhere.ts 处理所有条件构建

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  const startTime = Date.now();

  try {
    const { database: rawDatabase } = await params;
    
    // 统一转换为小写格式
    const database = rawDatabase.toLowerCase();

    // 验证数据库代码
    const validation = await validateDatabaseCode(database);
    if (!validation.isValid) {
      return NextResponse.json(
        { success: false, error: validation.error },
        { status: validation.status || 400 }
      );
    }

    // 权限检查 - 临时禁用用于测试
    /*
    const requiredLevel = getDatabaseAccessLevel(database);
    const hasAccess = await checkPermissions(requiredLevel);
    if (!hasAccess) {
      return NextResponse.json(
        { success: false, error: 'Permission denied' },
        { status: 403 }
      );
    }
    */

    const body: AdvancedSearchRequest = await request.json();
    const {
      conditions = [],
      sortBy = '',
      sortOrder = 'desc',
      globalKeyword = ''
    } = body;

    console.log('[Unified Search API] 接收到的参数:', {
      conditions: conditions.length,
      globalKeyword: globalKeyword ? '有' : '无',
      sortBy,
      sortOrder
    });

    // 使用全局翻页配置（性能优化）
    const requestedPage = body.page || 1;
    const requestedLimit = body.limit || 0;

    const { page, limit } = validatePaginationParams(requestedPage, requestedLimit);

    // 获取配置
    const config: DatabaseConfig = await getDatabaseConfig(database);

    // 使用已获取的配置
    const visibleFields = config.fields.filter(f => f.isVisible).map(f => f.fieldName);
    const sortableFields = config.fields.filter(f => f.isSortable).map(f => f.fieldName);

    // 获取动态表
    const table = await getDynamicTable(database);
    if (!isDrizzleTable(table)) {
      return NextResponse.json(
        { success: false, error: '模型未找到或无效' },
        { status: 500 }
      );
    }

    // 构建统一查询条件 - 完全使用Drizzle格式
    let where = buildAdvancedSearchWhere(conditions, config, table);

    // 处理全局搜索关键词
    if (globalKeyword && globalKeyword.trim()) {
      console.log('[Unified Search API] 处理全局搜索关键词:', globalKeyword);

      // 创建包含全局搜索的临时条件
      const globalSearchCondition: SearchCondition = {
        id: 'global_search',
        field: 'allFields',
        value: globalKeyword.trim(),
        logic: conditions.length > 0 ? 'AND' : undefined
      };

      // 将全局搜索条件添加到现有条件中
      const allConditions = [...conditions, globalSearchCondition];
      where = buildAdvancedSearchWhere(allConditions, config, table);
    }

    // 构建排序条件 - Drizzle格式
    const defaultSortField = sortableFields[0] || 'id';
    const sortField = sortBy || defaultSortField;
    const sortColumn = table[sortField];

    let orderByClause;
    const primaryKey = table.knumber || table.id; // 使用knumber作为主键，回退到id

    if (sortColumn) {
      // 使用指定排序 + 主键作为二级排序确保结果稳定性
      if (sortOrder === 'asc') {
        orderByClause = [asc(sortColumn), asc(primaryKey)];
      } else {
        orderByClause = [desc(sortColumn), desc(primaryKey)];
      }
    } else {
      // 默认使用主键降序排序
      orderByClause = [desc(primaryKey)];
    }

    // 计算总数
    const totalCountResult = await db
      .select({ count: count() })
      .from(table)
      .where(where);
    const totalCount = Number(totalCountResult[0]?.count || 0);

    // 查询数据
    const offset = (page - 1) * limit;

    const baseQuery = db
      .select()
      .from(table);

    const data = await (where ? baseQuery.where(where) : baseQuery)
      .orderBy(...orderByClause)
      .limit(limit)
      .offset(offset);

    // 过滤返回的字段，只返回可见字段
    const filteredData = data.map(item => {
      const filtered: Record<string, any> = { id: item.id };
      visibleFields.forEach(fieldName => {
        if (item.hasOwnProperty(fieldName)) {
          filtered[fieldName] = item[fieldName];
        }
      });
      return filtered;
    });

    return NextResponse.json({
      success: true,
      data: filteredData,
      pagination: buildPaginationResponse(page, limit, totalCount),
      config: {
        fields: config.fields,
        database: database,
      },
      search_info: {
        conditions_count: conditions.length,
        global_keyword: globalKeyword ? 'provided' : 'none',
        search_time: Date.now() - startTime,
      },
    });
    
  } catch (error) {
    console.error('Advanced search API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
