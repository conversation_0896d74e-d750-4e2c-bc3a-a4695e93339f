/**
 * 基于Drizzle的字段统计API
 * 替代原有的Prisma统计逻辑
 */

import { NextRequest, NextResponse } from 'next/server';
import { DrizzleSearchService } from '@/lib/services/drizzleSearchService';
import { validateDatabaseCode } from '@/lib/drizzleTableMapping';

/**
 * GET /api/drizzle-statistics/[database]/[field]
 * 获取指定字段的统计信息
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ database: string; field: string }> }
) {
  try {
    const { database, field } = await params;
    const { searchParams } = new URL(request.url);
    
    // 验证数据库代码
    const validation = validateDatabaseCode(database);
    if (!validation.isValid) {
      return NextResponse.json({
        success: false,
        error: validation.error || `不支持的数据库代码: ${database}`
      }, { status: validation.status || 400 });
    }
    
    // 解析查询参数
    const limit = parseInt(searchParams.get('limit') || '50');
    
    // 解析现有条件
    let conditions = [];
    const conditionsParam = searchParams.get('conditions');
    if (conditionsParam) {
      try {
        conditions = JSON.parse(conditionsParam);
      } catch (error) {
        console.warn('[DrizzleStatistics API] 解析conditions参数失败:', error);
      }
    }
    
    console.log('[DrizzleStatistics API] 统计请求:', {
      database,
      field,
      limit,
      conditions: conditions.length
    });
    
    // 获取字段统计
    const result = await DrizzleSearchService.getFieldStatistics(
      database,
      field,
      conditions,
      limit
    );
    
    console.log('[DrizzleStatistics API] 统计结果:', {
      field: result.field,
      itemCount: result.items.length,
      total: result.total
    });
    
    return NextResponse.json({
      success: true,
      data: result
    });
    
  } catch (error) {
    console.error('[DrizzleStatistics API] 错误:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取统计信息失败',
      data: {
        field: '',
        items: [],
        total: 0
      }
    }, { status: 500 });
  }
}

/**
 * POST /api/drizzle-statistics/[database]/[field]
 * 批量获取字段统计信息
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ database: string; field: string }> }
) {
  try {
    const { database, field } = await params;
    
    // 验证数据库代码
    const validation = validateDatabaseCode(database);
    if (!validation.isValid) {
      return NextResponse.json({
        success: false,
        error: validation.error || `不支持的数据库代码: ${database}`
      }, { status: validation.status || 400 });
    }
    
    const body = await request.json();
    const {
      conditions = [],
      limit = 50
    } = body;
    
    console.log('[DrizzleStatistics API] POST统计请求:', {
      database,
      field,
      conditions: conditions.length,
      limit
    });
    
    // 获取字段统计
    const result = await DrizzleSearchService.getFieldStatistics(
      database,
      field,
      conditions,
      limit
    );
    
    return NextResponse.json({
      success: true,
      data: result
    });
    
  } catch (error) {
    console.error('[DrizzleStatistics API] POST错误:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取统计信息失败',
      data: {
        field: '',
        items: [],
        total: 0
      }
    }, { status: 500 });
  }
}
