import { NextResponse } from 'next/server';
import { db } from '@/lib/prisma';

export async function GET() {
  const startTime = Date.now();
  
  try {
    // 测试数据库连接
    const connectionTest = await db.$queryRaw`SELECT 1 as test`;
    const connectionTime = Date.now() - startTime;
    
    // 测试数据库配置表查询
    const configStartTime = Date.now();
    const configCount = await db.databaseConfig.count({
      where: { isActive: true }
    });
    const configQueryTime = Date.now() - configStartTime;
    
    // 获取数据库版本信息
    const versionInfo = await db.$queryRaw`SELECT version() as version`;
    
    return NextResponse.json({
      success: true,
      status: 'healthy',
      checks: {
        connection: {
          status: 'ok',
          responseTime: `${connectionTime}ms`,
          result: connectionTest
        },
        configTable: {
          status: 'ok',
          responseTime: `${configQueryTime}ms`,
          count: configCount
        },
        database: {
          version: (versionInfo as any)[0]?.version || 'unknown'
        }
      },
      totalResponseTime: `${Date.now() - startTime}ms`,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    const errorTime = Date.now() - startTime;
    
    return NextResponse.json({
      success: false,
      status: 'unhealthy',
      error: {
        message: error instanceof Error ? error.message : 'Unknown database error',
        code: (error as any)?.code || 'UNKNOWN',
        responseTime: `${errorTime}ms`
      },
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
