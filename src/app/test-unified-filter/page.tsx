'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Loader2, Database, Filter, Search } from 'lucide-react';
import { UnifiedFilterPanel } from '@/components/UnifiedFilterPanel';
import { useUnifiedSearch, DatabaseFieldConfig } from '@/lib/hooks/useUnifiedSearch';

export default function TestUnifiedFilter() {
  const [database] = useState('us_pmn');
  const [config, setConfig] = useState<{ fields: DatabaseFieldConfig[] } | null>(null);
  const [configLoading, setConfigLoading] = useState(true);
  const [filterOpen, setFilterOpen] = useState(true);
  const [testResults, setTestResults] = useState<string[]>([]);

  // 使用统一搜索Hook
  const {
    searchState,
    executeSearch: _executeSearch,
    updateFromFilterPanel,
    getFieldStatistics,
    clearAllConditions
  } = useUnifiedSearch(database, config);

  // 加载配置
  useEffect(() => {
    const loadConfig = async () => {
      try {
        setConfigLoading(true);
        const response = await fetch(`/api/config/${database}`);
        const result = await response.json();
        
        if (result.success) {
          setConfig(result.data);
          addTestResult(`✅ 配置加载成功: ${result.data.fields.length} 个字段`);
          
          // 统计可筛选字段
          const filterableFields = result.data.fields.filter((f: any) => f.isFilterable);
          addTestResult(`📊 可筛选字段: ${filterableFields.length} 个`);
          
          filterableFields.slice(0, 5).forEach((field: any) => {
            addTestResult(`  - ${field.displayName} (${field.filterType})`);
          });
        } else {
          addTestResult(`❌ 配置加载失败: ${result.error}`);
        }
      } catch (error) {
        addTestResult(`❌ 配置加载异常: ${error}`);
      } finally {
        setConfigLoading(false);
      }
    };

    loadConfig();
  }, [database]);

  const addTestResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  // 测试Filter面板功能
  const testFilterPanel = async () => {
    addTestResult('🔧 测试Filter面板功能...');

    if (!config) {
      addTestResult('❌ 配置未加载，无法测试');
      return;
    }

    try {
      // 模拟用户选择筛选条件
      const testFilters = {
        applicant: 'Abbott',
        thirdparty: ['Y']
      };

      addTestResult(`📝 应用测试筛选条件: ${JSON.stringify(testFilters)}`);
      
      const result = await updateFromFilterPanel(testFilters);
      
      if (result?.success) {
        addTestResult(`✅ Filter面板测试成功: ${result.data?.length || 0} 条结果`);
        addTestResult(`📊 搜索条件数: ${searchState.conditions.length}`);
        addTestResult(`📈 统计缓存: ${searchState.statisticsCache.size} 个字段`);
      } else {
        addTestResult(`❌ Filter面板测试失败: ${result?.error}`);
      }
    } catch (error) {
      addTestResult(`❌ Filter面板测试异常: ${error}`);
    }
  };

  // 测试统计数据共享
  const testStatisticsSharing = () => {
    addTestResult('🔧 测试统计数据共享...');

    if (!config) {
      addTestResult('❌ 配置未加载，无法测试统计');
      return;
    }

    const statisticsFields = config.fields.filter(f => f.isStatisticsEnabled);
    addTestResult(`📊 启用统计的字段: ${statisticsFields.length} 个`);

    statisticsFields.slice(0, 3).forEach(field => {
      const stats = getFieldStatistics(field.fieldName);
      if (stats) {
        addTestResult(`  - ${field.displayName}: ${stats.items.length} 个选项, 总计 ${stats.total}`);
      } else {
        addTestResult(`  - ${field.displayName}: 无统计数据`);
      }
    });
  };

  // 测试清空功能
  const testClearAll = async () => {
    addTestResult('🔧 测试清空所有条件...');

    try {
      await clearAllConditions();
      addTestResult('✅ 所有条件已清空');
      addTestResult(`📊 当前条件数: ${searchState.conditions.length}`);
    } catch (error) {
      addTestResult(`❌ 清空失败: ${error}`);
    }
  };

  return (
    <div className="flex min-h-screen">
      {/* Filter Panel */}
      <UnifiedFilterPanel
        database={database}
        config={config}
        isOpen={filterOpen}
        onToggle={() => setFilterOpen(!filterOpen)}
        className="fixed top-0 left-0 bottom-0 z-50 overflow-hidden"
      />

      {/* Main Content */}
      <div className={`flex-1 transition-all duration-300 ${filterOpen ? 'ml-72' : 'ml-16'} p-6 space-y-6`}>
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">统一Filter面板测试</h1>
            <div className="flex items-center gap-2 mt-2">
              <Badge variant="outline">数据库: {database}</Badge>
              <Badge variant={configLoading ? "secondary" : "default"}>
                {configLoading ? "加载中..." : "已加载"}
              </Badge>
            </div>
          </div>
          
          <Button variant="outline" onClick={() => setFilterOpen(!filterOpen)}>
            <Filter className="h-4 w-4 mr-2" />
            {filterOpen ? '隐藏' : '显示'} Filter面板
          </Button>
        </div>

        {/* 搜索状态卡片 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              当前搜索状态
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <h4 className="font-medium mb-2">搜索条件</h4>
                <p className="text-2xl font-bold text-blue-600">
                  {searchState.conditions.length}
                </p>
                <p className="text-sm text-gray-600">个活跃条件</p>
              </div>
              <div>
                <h4 className="font-medium mb-2">统计缓存</h4>
                <p className="text-2xl font-bold text-green-600">
                  {searchState.statisticsCache.size}
                </p>
                <p className="text-sm text-gray-600">个字段缓存</p>
              </div>
              <div>
                <h4 className="font-medium mb-2">分页信息</h4>
                <p className="text-2xl font-bold text-purple-600">
                  {searchState.pagination.page}
                </p>
                <p className="text-sm text-gray-600">
                  共 {searchState.pagination.total.toLocaleString()} 条
                </p>
              </div>
              <div>
                <h4 className="font-medium mb-2">加载状态</h4>
                <div className="flex items-center gap-2">
                  {searchState.isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
                  <p className="text-sm">
                    {searchState.isLoading ? '搜索中...' : '就绪'}
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 测试按钮 */}
        <Card>
          <CardHeader>
            <CardTitle>功能测试</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button 
                onClick={testFilterPanel} 
                disabled={configLoading || !config}
                className="flex items-center gap-2"
              >
                <Filter className="h-4 w-4" />
                测试Filter面板
              </Button>

              <Button 
                onClick={testStatisticsSharing} 
                disabled={!config}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Search className="h-4 w-4" />
                测试统计共享
              </Button>

              <Button 
                onClick={testClearAll} 
                disabled={searchState.isLoading}
                variant="outline"
              >
                清空所有条件
              </Button>

              <Button 
                onClick={() => setTestResults([])} 
                variant="ghost"
              >
                清空日志
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 当前搜索条件显示 */}
        {searchState.conditions.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>当前搜索条件</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {searchState.conditions.map((condition, index) => (
                  <div key={condition.id} className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                    {index > 0 && (
                      <Badge variant="secondary" className="text-xs">
                        {condition.logic || 'AND'}
                      </Badge>
                    )}
                    <span className="font-medium">{condition.field}:</span>
                    <span className="text-gray-600">
                      {Array.isArray(condition.value) 
                        ? condition.value.join(', ') 
                        : String(condition.value)
                      }
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* 测试日志 */}
        <Card>
          <CardHeader>
            <CardTitle>测试日志</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-gray-50 p-4 rounded-lg max-h-96 overflow-y-auto">
              {testResults.length === 0 ? (
                <p className="text-gray-500">暂无测试结果</p>
              ) : (
                <div className="space-y-1">
                  {testResults.map((result, index) => (
                    <div key={index} className="text-sm font-mono">
                      {result}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
