"use client";

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import AdvancedSearch, { type SearchCondition } from '@/components/AdvancedSearch';
import { MultiSelect } from '@/components/ui/multi-select';

export default function TestUnifiedSearch() {
  const [metadata, setMetadata] = useState<Record<string, string[]>>({});
  const [metadataWithCounts, setMetadataWithCounts] = useState<Record<string, Array<{ value: string; count: number }>>>({});
  const [advancedConditions, setAdvancedConditions] = useState<SearchCondition[]>([]);
  const [filterPanelValue, setFilterPanelValue] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [testResults, setTestResults] = useState<string[]>([]);

  const database = 'us_pmn';

  useEffect(() => {
    const fetchInitialMetadata = async () => {
      try {
        const response = await fetch(`/api/meta/${database}`);
        const data = await response.json();
        
        if (data.success) {
          setMetadata(data.data);
          setMetadataWithCounts(data.dataWithCounts);
          addTestResult('✅ Initial metadata loaded successfully');
          addTestResult(`📊 Third Party options: ${JSON.stringify(data.dataWithCounts.thirdparty)}`);
        }
      } catch (error) {
        addTestResult(`❌ Failed to fetch initial metadata: ${error}`);
      } finally {
        setLoading(false);
      }
    };

    fetchInitialMetadata();
  }, []);

  const addTestResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testFilteredMetadata = async (conditions: SearchCondition[], filters: Record<string, any>) => {
    try {
      addTestResult(`🔍 Testing filtered metadata with conditions: ${conditions.length}, filters: ${Object.keys(filters).length}`);
      
      const response = await fetch(`/api/meta/${database}/filtered`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          conditions,
          filters
        }),
      });

      const result = await response.json();
      if (result.success) {
        setMetadataWithCounts(result.dataWithCounts);
        addTestResult('✅ Filtered metadata updated successfully');
        addTestResult(`📊 Updated Third Party options: ${JSON.stringify(result.dataWithCounts.thirdparty)}`);
      } else {
        addTestResult(`❌ Filtered metadata API failed: ${result.error}`);
      }
    } catch (error) {
      addTestResult(`❌ Error testing filtered metadata: ${error}`);
    }
  };

  const handleAdvancedSearch = async (conditions: SearchCondition[]) => {
    setAdvancedConditions(conditions);
    addTestResult(`🔧 Advanced Search triggered with ${conditions.length} conditions`);
    
    // Test the filtered metadata API
    await testFilteredMetadata(conditions, {});
  };

  const handleFilterPanelChange = async (value: string[]) => {
    setFilterPanelValue(value);
    addTestResult(`🔧 Filter Panel changed: ${value.join(', ')}`);
    
    // Test the filtered metadata API with filter panel values
    const filters = value.length > 0 ? { thirdparty: value } : {};
    await testFilteredMetadata([], filters);
  };

  const handleClearAll = async () => {
    setAdvancedConditions([]);
    setFilterPanelValue([]);
    addTestResult('🧹 Clear All triggered');
    
    // Reset to original metadata
    const response = await fetch(`/api/meta/${database}`);
    const data = await response.json();
    if (data.success) {
      setMetadataWithCounts(data.dataWithCounts);
      addTestResult('✅ Metadata reset to original state');
      addTestResult(`📊 Reset Third Party options: ${JSON.stringify(data.dataWithCounts.thirdparty)}`);
    }
  };

  const testUnifiedBehavior = async () => {
    addTestResult('🧪 Starting unified behavior test...');
    
    // Step 1: Apply filter panel selection
    await handleFilterPanelChange(['Y']);
    
    // Step 2: Add advanced search condition
    const testCondition: SearchCondition = {
      id: 'test-1',
      field: 'devicename',
      value: 'dental',
      logic: 'AND'
    };
    await handleAdvancedSearch([testCondition]);
    
    // Step 3: Clear all and verify reset
    setTimeout(async () => {
      await handleClearAll();
      addTestResult('🧪 Unified behavior test completed');
    }, 2000);
  };

  const availableFields = [
    {
      fieldName: 'thirdparty',
      displayName: 'Third Party',
      fieldType: 'text',
      filterType: 'multi_select',
      isAdvancedSearchable: true
    },
    {
      fieldName: 'devicename',
      displayName: 'Device Name',
      fieldType: 'text',
      filterType: 'input',
      isAdvancedSearchable: true
    }
  ];

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">Loading...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Unified Search System Test</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Button onClick={testUnifiedBehavior} className="w-full">
              🧪 Run Unified Behavior Test
            </Button>
            <Button onClick={handleClearAll} variant="outline" className="w-full">
              🧹 Clear All
            </Button>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Filter Panel Simulation</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <label className="text-sm font-medium">Third Party</label>
              <MultiSelect
                options={metadataWithCounts.thirdparty?.map(item => ({
                  value: item.value,
                  label: item.value,
                  count: item.count
                })) || []}
                value={filterPanelValue}
                onValueChange={handleFilterPanelChange}
                placeholder="Select Third Party"
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Advanced Search</CardTitle>
          </CardHeader>
          <CardContent>
            <AdvancedSearch
              onSearch={handleAdvancedSearch}
              onClear={() => setAdvancedConditions([])}
              availableFields={availableFields}
              currentConditions={advancedConditions}
              metadata={metadata}
              metadataWithCounts={metadataWithCounts}
            />
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Current State</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div><strong>Filter Panel Value:</strong> {filterPanelValue.join(', ') || 'None'}</div>
            <div><strong>Advanced Conditions:</strong> {advancedConditions.length}</div>
            <div><strong>Third Party Statistics:</strong></div>
            <pre className="bg-gray-100 p-2 rounded text-sm">
              {JSON.stringify(metadataWithCounts.thirdparty, null, 2)}
            </pre>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Test Results Log</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-gray-100 p-4 rounded max-h-96 overflow-y-auto">
            {testResults.map((result, index) => (
              <div key={index} className="text-sm font-mono mb-1">
                {result}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
