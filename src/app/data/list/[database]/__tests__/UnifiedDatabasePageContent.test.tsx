import { render, screen, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { useSearchParams } from 'next/navigation';
import UnifiedDatabasePageContent from '../UnifiedDatabasePageContent';
import { useUnifiedSearch } from '@/lib/hooks/useUnifiedSearch';

// Mock dependencies
vi.mock('next/navigation', () => ({
  useSearchParams: vi.fn(),
}));

vi.mock('@/lib/hooks/useUnifiedSearch', () => ({
  useUnifiedSearch: vi.fn(),
}));

vi.mock('@/components/UnifiedFilterPanel', () => ({
  UnifiedFilterPanel: function MockUnifiedFilterPanel() {
    return <div data-testid="unified-filter-panel">Filter Panel</div>;
  },
}));

vi.mock('@/components/AdvancedSearch', () => ({
  default: function MockAdvancedSearch() {
    return <div data-testid="advanced-search">Advanced Search</div>;
  },
}));

vi.mock('@/components/SearchChips', () => ({
  default: function MockSearchChips() {
    return <div data-testid="search-chips">Search Chips</div>;
  },
}));

vi.mock('@/components/DataTable', () => ({
  DataTable: function MockDataTable() {
    return <div data-testid="data-table">Data Table</div>;
  },
}));

// Mock fetch for config API
global.fetch = vi.fn();

const mockSearchParams = new URLSearchParams();
const mockUseUnifiedSearch = {
  searchState: {
    conditions: [],
    pagination: {
      page: 1,
      limit: 20,
      total: 172806,
    },
    statisticsCache: new Map(),
    isLoading: false,
  },
  executeSearch: vi.fn(),
  updateFromFilterPanel: vi.fn(),
  updateFromAdvancedSearch: vi.fn(),
  updateSorting: vi.fn(),
  updatePagination: vi.fn(),
  clearAllConditions: vi.fn(),
};

describe('UnifiedDatabasePageContent', () => {
  beforeEach(() => {
    (useSearchParams as any).mockReturnValue(mockSearchParams);
    (useUnifiedSearch as any).mockReturnValue(mockUseUnifiedSearch);

    // Mock successful config fetch
    (global.fetch as any).mockResolvedValue({
      json: () => Promise.resolve({
        success: true,
        data: {
          fields: [
            {
              fieldName: 'test_field',
              displayName: 'Test Field',
              isVisible: true,
              isFilterable: true,
              isAdvancedSearchable: true,
              listOrder: 1,
            },
          ],
        },
      }),
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should not render the Search Status card', async () => {
    render(<UnifiedDatabasePageContent database="us_pmn" />);

    await waitFor(() => {
      expect(screen.queryByText('Search Status')).not.toBeInTheDocument();
      expect(screen.queryByText('Active Conditions')).not.toBeInTheDocument();
      expect(screen.queryByText('Current Page')).not.toBeInTheDocument();
      expect(screen.queryByText('Statistics Cache')).not.toBeInTheDocument();
    });
  });

  it('should render the results count at the bottom', async () => {
    render(<UnifiedDatabasePageContent database="us_pmn" />);

    await waitFor(() => {
      expect(screen.getByText('172,806')).toBeInTheDocument();
      expect(screen.getByText('results found')).toBeInTheDocument();
    });
  });

  it('should format large numbers with commas', async () => {
    const mockSearchStateWithLargeNumber = {
      ...mockUseUnifiedSearch,
      searchState: {
        ...mockUseUnifiedSearch.searchState,
        pagination: {
          ...mockUseUnifiedSearch.searchState.pagination,
          total: 1234567,
        },
      },
    };

    (useUnifiedSearch as any).mockReturnValue(mockSearchStateWithLargeNumber);

    render(<UnifiedDatabasePageContent database="us_pmn" />);

    await waitFor(() => {
      expect(screen.getByText('1,234,567')).toBeInTheDocument();
    });
  });

  it('should update results count when search state changes', async () => {
    const { rerender } = render(<UnifiedDatabasePageContent database="us_pmn" />);

    await waitFor(() => {
      expect(screen.getByText('172,806')).toBeInTheDocument();
    });

    // Update the mock to return different total
    const updatedMockSearchState = {
      ...mockUseUnifiedSearch,
      searchState: {
        ...mockUseUnifiedSearch.searchState,
        pagination: {
          ...mockUseUnifiedSearch.searchState.pagination,
          total: 50000,
        },
      },
    };

    (useUnifiedSearch as any).mockReturnValue(updatedMockSearchState);

    rerender(<UnifiedDatabasePageContent database="us_pmn" />);

    await waitFor(() => {
      expect(screen.getByText('50,000')).toBeInTheDocument();
    });
  });

  it('should render all main components except Search Status card', async () => {
    render(<UnifiedDatabasePageContent database="us_pmn" />);

    await waitFor(() => {
      // Should render these components
      expect(screen.getByTestId('unified-filter-panel')).toBeInTheDocument();
      expect(screen.getByTestId('advanced-search')).toBeInTheDocument();
      expect(screen.getByTestId('data-table')).toBeInTheDocument();
      
      // Should render database title
      expect(screen.getByText('US_PMN Database')).toBeInTheDocument();
      
      // Should render results count
      expect(screen.getByText('172,806')).toBeInTheDocument();
      expect(screen.getByText('results found')).toBeInTheDocument();
      
      // Should NOT render Search Status card
      expect(screen.queryByText('Search Status')).not.toBeInTheDocument();
    });
  });

  it('should handle zero results correctly', async () => {
    const mockSearchStateWithZeroResults = {
      ...mockUseUnifiedSearch,
      searchState: {
        ...mockUseUnifiedSearch.searchState,
        pagination: {
          ...mockUseUnifiedSearch.searchState.pagination,
          total: 0,
        },
      },
    };

    (useUnifiedSearch as any).mockReturnValue(mockSearchStateWithZeroResults);

    render(<UnifiedDatabasePageContent database="us_pmn" />);

    await waitFor(() => {
      expect(screen.getByText('0')).toBeInTheDocument();
      expect(screen.getByText('results found')).toBeInTheDocument();
    });
  });
});
