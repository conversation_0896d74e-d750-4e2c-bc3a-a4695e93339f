"use client";

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import SearchChips from '@/components/SearchChips';
import { useSearchChips } from '@/hooks/useSearchChips';
import { SearchCondition } from '@/lib/api';

export default function TestSearchChipsPage() {
  // Mock search state
  const [filters, setFilters] = useState<Record<string, unknown>>({
    allFields: 'medical device',
    productName: 'cardiac pacemaker',
    companyName: 'Medtronic',
    status: 'active'
  });

  const [advancedConditions, setAdvancedConditions] = useState<SearchCondition[]>([
    {
      id: '1',
      field: 'approvalDate',
      value: { from: '2020-01-01', to: '2023-12-31' },
      logic: 'AND'
    },
    {
      id: '2',
      field: 'riskLevel',
      value: ['Class III', 'Class II'],
      logic: 'AND'
    }
  ]);

  const [sortBy, setSortBy] = useState<string>('approvalDate');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Field configuration
  const fieldConfigs = [
    { fieldName: 'allFields', displayName: 'Comprehensive Search' },
    { fieldName: 'productName', displayName: 'Product Name' },
    { fieldName: 'companyName', displayName: 'Company Name' },
    { fieldName: 'status', displayName: 'Status' },
    { fieldName: 'approvalDate', displayName: 'Approval Date' },
    { fieldName: 'riskLevel', displayName: 'Risk Level' },
  ];

  // Use search chips hook
  const searchChips = useSearchChips({
    filters,
    advancedConditions,
    sortBy,
    sortOrder,
    fieldConfigs,
    onFilterChange: (key: string, value: unknown) => {
      setFilters(prev => {
        const newFilters = { ...prev };
        if (value === undefined || value === null || value === '') {
          delete newFilters[key];
        } else {
          newFilters[key] = value;
        }
        return newFilters;
      });
    },
    onAdvancedConditionRemove: (conditionId: string) => {
      setAdvancedConditions(prev => prev.filter(c => c.id !== conditionId));
    },
    onSortChange: (newSortBy?: string, newSortOrder?: 'asc' | 'desc') => {
      setSortBy(newSortBy || '');
      setSortOrder(newSortOrder || 'desc');
    },
    onClearAll: () => {
      setFilters({});
      setAdvancedConditions([]);
      setSortBy('');
      setSortOrder('desc');
    }
  });

  // Add new filter
  const addFilter = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  // Add new advanced search condition
  const addAdvancedCondition = () => {
    const newCondition: SearchCondition = {
      id: Date.now().toString(),
      field: 'category',
      value: 'implantable device',
      logic: 'AND'
    };
    setAdvancedConditions(prev => [...prev, newCondition]);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Search Chips Component Test</CardTitle>
            <p className="text-gray-600">
              Test removable filter tags / search chips functionality
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Search chips display */}
            <div>
              <Label className="text-base font-medium mb-3 block">Current Search Conditions:</Label>
              <div className="border rounded-lg">
                <SearchChips
                  chips={searchChips.chips}
                  onRemoveChip={searchChips.handleRemoveChip}
                  onClearAll={searchChips.handleClearAll}
                  showClearAll={true}
                  maxDisplay={10}
                  compact={false}
                />
                {!searchChips.hasActiveFilters && (
                  <div className="p-4 text-gray-500 text-center">
                    No search conditions
                  </div>
                )}
              </div>
            </div>

            {/* Compact mode display */}
            <div>
              <Label className="text-base font-medium mb-3 block">Compact Mode:</Label>
              <div className="border rounded-lg">
                <SearchChips
                  chips={searchChips.chips}
                  onRemoveChip={searchChips.handleRemoveChip}
                  onClearAll={searchChips.handleClearAll}
                  showClearAll={true}
                  maxDisplay={5}
                  compact={true}
                />
                {!searchChips.hasActiveFilters && (
                  <div className="p-2 text-gray-500 text-center text-sm">
                    No search conditions
                  </div>
                )}
              </div>
            </div>

            {/* Action buttons */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button
                onClick={() => addFilter('deviceClass', 'Class III')}
                variant="outline"
              >
                Add Device Class
              </Button>
              <Button
                onClick={() => addFilter('manufacturer', 'Johnson & Johnson')}
                variant="outline"
              >
                Add Manufacturer
              </Button>
              <Button
                onClick={addAdvancedCondition}
                variant="outline"
              >
                Add Advanced Condition
              </Button>
              <Button
                onClick={() => {
                  setSortBy('productName');
                  setSortOrder('asc');
                }}
                variant="outline"
              >
                Set Sorting
              </Button>
            </div>

            {/* Status information */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <Card>
                <CardContent className="p-4">
                  <div className="font-medium text-gray-700">Filter Count</div>
                  <div className="text-2xl font-bold text-blue-600">
                    {Object.keys(filters).length}
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="font-medium text-gray-700">Advanced Conditions Count</div>
                  <div className="text-2xl font-bold text-green-600">
                    {advancedConditions.length}
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="font-medium text-gray-700">Total Tags Count</div>
                  <div className="text-2xl font-bold text-purple-600">
                    {searchChips.chipCount}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Current state details */}
            <div className="space-y-4">
              <div>
                <Label className="font-medium">Current Filters:</Label>
                <pre className="mt-2 p-3 bg-gray-100 rounded text-xs overflow-auto">
                  {JSON.stringify(filters, null, 2)}
                </pre>
              </div>
              <div>
                <Label className="font-medium">Advanced Search Conditions:</Label>
                <pre className="mt-2 p-3 bg-gray-100 rounded text-xs overflow-auto">
                  {JSON.stringify(advancedConditions, null, 2)}
                </pre>
              </div>
              <div>
                <Label className="font-medium">Sort Settings:</Label>
                <pre className="mt-2 p-3 bg-gray-100 rounded text-xs overflow-auto">
                  {JSON.stringify({ sortBy, sortOrder }, null, 2)}
                </pre>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
