import { db } from '../lib/drizzle';
import { databaseConfigs, fieldConfigs } from './schema';

// 数据库配置数据
const dbConfigs = [
  {
    code: 'freePat',
    name: '医药专利',
    category: '药物研发',
    description: '医药专利信息及原文下载',
    accessLevel: 'free',
    isActive: true,
    sortOrder: 1,
  },
  {
    code: 'deviceCNImported',
    name: '医疗器械模板',
    category: 'Regulation',
    description: '新建医疗器械数据库时的基础模板，包含完整的字段配置和表结构',
    accessLevel: 'free',
    isActive: true,
    sortOrder: 2,
  },
  {
    code: 'deviceCNEvaluation',
    name: '中国大陆审评',
    category: 'Regulation',
    description: '医疗器械审评进度跟踪、审评结论查询',
    accessLevel: 'premium',
    isActive: true,
    sortOrder: 3,
  },
  {
    code: 'deviceHK',
    name: '中国香港上市',
    category: 'Regulation',
    description: '中国香港已上市的医疗器械信息',
    accessLevel: 'premium',
    isActive: true,
    sortOrder: 4,
  },
  {
    code: 'deviceUS',
    name: '美国上市',
    category: '全球器械',
    description: '美国FDA批准的医疗器械信息',
    accessLevel: 'premium',
    isActive: true,
    sortOrder: 5,
    tableName: 'us_pmn',
    modelName: 'USPremarketNotification',
  },
  {
    code: 'deviceJP',
    name: '日本上市',
    category: '全球器械',
    description: '日本PMDA批准的医疗器械信息',
    accessLevel: 'premium',
    isActive: true,
    sortOrder: 6,
  },
  {
    code: 'deviceUK',
    name: '英国上市',
    category: '全球器械',
    description: '英国MHRA批准的医疗器械信息',
    accessLevel: 'premium',
    isActive: true,
    sortOrder: 7,
  },
  {
    code: 'us_class',
    name: '美国器械分类',
    category: '全球器械',
    description: '美国FDA医疗器械分类目录',
    accessLevel: 'free',
    isActive: true,
    sortOrder: 8,
    tableName: 'us_class',
    modelName: 'USClass',
  },
  {
    code: 'us_pmn',
    name: '美国510(k)',
    category: '全球器械',
    description: '美国FDA 510(k)上市前通知数据',
    accessLevel: 'premium',
    isActive: true,
    sortOrder: 9,
    tableName: 'us_pmn',
    modelName: 'USPremarketNotification',
  }
];

// 基础字段配置（用于 us_pmn 数据库）
const usPmnFieldConfigs = [
  {
    databaseCode: 'us_pmn',
    fieldName: 'knumber',
    displayName: '510(k) Number',
    fieldType: 'text' as const,
    isVisible: true,
    isSearchable: true,
    isFilterable: true,
    isSortable: true,
    searchType: 'contains' as const,
    filterType: 'input' as const,
    listOrder: 1,
    isActive: true,
  },
  {
    databaseCode: 'us_pmn',
    fieldName: 'applicant',
    displayName: 'Applicant',
    fieldType: 'text' as const,
    isVisible: true,
    isSearchable: true,
    isFilterable: true,
    isSortable: true,
    searchType: 'contains' as const,
    filterType: 'input' as const,
    listOrder: 2,
    isActive: true,
  },
  {
    databaseCode: 'us_pmn',
    fieldName: 'devicename',
    displayName: 'Device Name',
    fieldType: 'text' as const,
    isVisible: true,
    isSearchable: true,
    isFilterable: false,
    isSortable: true,
    searchType: 'contains' as const,
    filterType: 'input' as const,
    listOrder: 3,
    isActive: true,
  },
  {
    databaseCode: 'us_pmn',
    fieldName: 'decision',
    displayName: 'Decision',
    fieldType: 'select' as const,
    isVisible: true,
    isSearchable: false,
    isFilterable: true,
    isSortable: true,
    searchType: 'exact' as const,
    filterType: 'select' as const,
    listOrder: 4,
    isActive: true,
  },
  {
    databaseCode: 'us_pmn',
    fieldName: 'decisiondate',
    displayName: 'Decision Date',
    fieldType: 'date' as const,
    isVisible: true,
    isSearchable: false,
    isFilterable: true,
    isSortable: true,
    searchType: 'date_range' as const,
    filterType: 'date_range' as const,
    listOrder: 5,
    isActive: true,
  },
  {
    databaseCode: 'us_pmn',
    fieldName: 'productcode',
    displayName: 'Product Code',
    fieldType: 'text' as const,
    isVisible: true,
    isSearchable: true,
    isFilterable: true,
    isSortable: true,
    searchType: 'exact' as const,
    filterType: 'select' as const,
    listOrder: 6,
    isActive: true,
  }
];

// 基础字段配置（用于 us_class 数据库）
const usClassFieldConfigs = [
  {
    databaseCode: 'us_class',
    fieldName: 'productcode',
    displayName: 'Product Code',
    fieldType: 'text' as const,
    isVisible: true,
    isSearchable: true,
    isFilterable: true,
    isSortable: true,
    searchType: 'exact' as const,
    filterType: 'select' as const,
    listOrder: 1,
    isActive: true,
  },
  {
    databaseCode: 'us_class',
    fieldName: 'devicename',
    displayName: 'Device Name',
    fieldType: 'text' as const,
    isVisible: true,
    isSearchable: true,
    isFilterable: false,
    isSortable: true,
    searchType: 'contains' as const,
    filterType: 'input' as const,
    listOrder: 2,
    isActive: true,
  },
  {
    databaseCode: 'us_class',
    fieldName: 'deviceclass',
    displayName: 'Device Class',
    fieldType: 'select' as const,
    isVisible: true,
    isSearchable: false,
    isFilterable: true,
    isSortable: true,
    searchType: 'exact' as const,
    filterType: 'select' as const,
    listOrder: 3,
    isActive: true,
  },
  {
    databaseCode: 'us_class',
    fieldName: 'medicalspecialty',
    displayName: 'Medical Specialty',
    fieldType: 'text' as const,
    isVisible: true,
    isSearchable: true,
    isFilterable: true,
    isSortable: true,
    searchType: 'contains' as const,
    filterType: 'select' as const,
    listOrder: 4,
    isActive: true,
  },
  {
    databaseCode: 'us_class',
    fieldName: 'regulationnumber',
    displayName: 'Regulation Number',
    fieldType: 'text' as const,
    isVisible: true,
    isSearchable: true,
    isFilterable: true,
    isSortable: true,
    searchType: 'exact' as const,
    filterType: 'input' as const,
    listOrder: 5,
    isActive: true,
  }
];

async function main() {
  try {
    console.error('🌱 开始 Drizzle 种子数据插入...');

    // 插入数据库配置
    console.error('📋 插入数据库配置...');
    const createdDbConfigs = await db.insert(databaseConfigs)
      .values(dbConfigs.map(config => ({
        ...config,
        createdAt: new Date(),
        updatedAt: new Date(),
      })))
      .onConflictDoNothing()
      .returning();

    console.error(`✅ 插入数据库配置: ${createdDbConfigs.length} 条`);

    // 插入字段配置
    console.error('🔧 插入字段配置...');
    const allFieldConfigs = [...usPmnFieldConfigs, ...usClassFieldConfigs];
    
    const createdFieldConfigs = await db.insert(fieldConfigs)
      .values(allFieldConfigs.map(config => ({
        ...config,
        sortOrder: 0,
        listOrder: config.listOrder || 0,
        detailOrder: 0,
        isStatisticsEnabled: false,
        statisticsOrder: 0,
        statisticsType: 'count' as const,
        statisticsDefaultLimit: 5,
        statisticsMaxLimit: 50,
        isExportable: true,
        exportOrder: 0,
        filterOrder: 0,
        todetail: false,
        isAdvancedSearchable: false,
        createdAt: new Date(),
        updatedAt: new Date(),
      })))
      .onConflictDoNothing()
      .returning();

    console.error(`✅ 插入字段配置: ${createdFieldConfigs.length} 条`);

    console.error('✅ Drizzle 种子数据插入完成！');
    console.error(`📊 总计插入:`);
    console.error(`  - 数据库配置: ${createdDbConfigs.length} 条`);
    console.error(`  - 字段配置: ${createdFieldConfigs.length} 条`);

  } catch (error) {
    console.error('❌ Drizzle 种子数据插入失败:', error);
    process.exit(1);
  }
}

main();
